@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=EB+Garamond:ital,wght@0,400..800;1,400..800&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");

* {
  padding: auto;
  margin: 0;
  font-family: "Montserrat";
}

body {
  overflow-x: hidden;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .main-menu ul li a {
    color: #fff !important;
    font-size: 13px !important;
  }
  .main-menu ul li a {
    padding: 6px !important;
  }
  .header-logo h3 {
    font-size: 16px !important;
  }
  .sticky-social {
    top: 140px !important;
  }
  .flex-column {
    margin-right: 16px !important;
  }
  .main-menu ul li .submenu {
    padding: 0;
    inset-inline-start: -40% !important;
    min-width: 200px !important;
  }
}

.main-menu ul li .submenu li .submenu {
  inset-inline-start: 100% !important;
}
.main-menu ul li a {
  color: #fff !important;
}
.logo-and-menu {
  padding: 0px !important;
}
.sticky-social {
  position: fixed;
  z-index: 999;
  top: 220px;
  width: 3%;
  right: 0px;
}

.header-logo {
  display: flex;
  align-items: center;
}
.header-logo h3 {
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}
.nba {
  width: 100%;
  margin-bottom: 6px !important;
}
.header-logo span {
  font-weight: 200;
  font-size: 14px;
}
.sticky-social img {
  margin-bottom: 5px;
}
.shrink-h3 {
  font-size: 18px !important;
}
#header-sticky {
  padding: 4px 7px;
  background: #000000ac;
}
.sticky {
  background: #000000fc !important;
  padding-top: 0px !important;
}
.shrink {
  width: 110px !important; /* Adjust this value to your desired size */
  height: auto !important; /* Maintain aspect ratio */
  transition: width 0.3s ease !important; /* Add a smooth transition effect */
}
.shrink-font {
  font-size: 14px !important; /* Adjust this value to your desired font size */
  transition: font-size 0.3s ease !important; /* Add a smooth transition effect */
}

.main-menu ul li.has-dropdown > a {
  text-decoration: none !important;
  padding: 9px !important;
}
.main-menu ul li .submenu li a {
  color: #000 !important;
  text-decoration: none !important;
}
header {
  position: relative;
  z-index: 99;
}
.top-menu {
  display: flex;
  justify-content: space-between;
}
.logo-con {
  padding-top: 20px !important;
}
.before-dropdown::before {
  content: "";
  top: 135%;
  transform: rotate(180deg);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 16px solid white;
  position: absolute;
  margin-left: 40px;
}

.dropdown-sub-menu {
  list-style: none;
  padding-left: 0px;
}
.navbar-expand-lg .navbar-nav .dropdown-menu {
  position: absolute;
}
.dropdown-menu.visible {
  display: block;
}
.main-menu ul li a {
  padding: 10px !important;
}

.main-menu ul li.has-dropdown > a::after {
  content: "\25BE" !important;
  -webkit-transform: translateY(1px);
  -moz-transform: translateY(1px);
  -ms-transform: translateY(1px);
  -o-transform: translateY(1px);
  transform: translateY(1px);
  font-size: 20px !important;
  font-family: var(--bd-ff-fontawesome);
  font-weight: 600;
  margin-inline-start: 5px;
  display: inline-block;
}

.main-menu ul li a {
  text-decoration: none;
}
.navbar-collapse {
  flex-grow: 0 !important;
}
.sub-menu {
  display: block !important;
}
.header-left {
  display: flex;
  justify-content: space-around;
  gap: 10px !important;
}
.main-menu ul li .submenu {
  padding: 0;
  inset-inline-start: -40% !important;
}
.main-menu ul li .submenu li a:hover {
  background-color: #000 !important;
  color: #fff !important;
}
.header-main {
  display: block !important;
}
.sub-topmenu {
  display: flex;
  gap: 40px;
}
.offcanvas__close button {
  background-color: #ef1c23 !important;
}
.offcanvas__info {
  border: none !important;
}
.nav-pills .nav-link {
  font-size: 16px;
  text-align: left;
  margin-bottom: 14px;
}
.top-menu {
  position: relative;
  z-index: 9;
}
.sub-topmenu h6 {
  color: #fff;
  font-size: 14px;
}
.topmenu-btn {
  border-radius: 5px;
  background-color: rgba(239, 28, 35, 1);
  color: #fff;
  padding: 4px 20px;
  font-size: 15px;
  border: none;
  font-weight: 500;
  transition: 0.2s all;
}
.topmenu-btn:hover {
  background-color: #fff;
  border: 1px solid rgba(239, 28, 35, 1);
  color: rgba(239, 28, 35, 1);
}

.navbar-expand-lg .navbar-collapse {
  display: flex;
  flex-basis: auto;
  justify-content: end;
}

.navbar-light .navbar-nav .nav-link {
  color: #fff !important;
  font-family: "Montserrat";
}



.navbar-expand-lg .navbar-nav .nav-link {
  padding: 0px 20px !important;
}
.navbar-expand-lg .navbar-collapse {
  display: flex;
  flex-basis: auto;
  flex-direction: column;
}
.navbar-expand-lg .navbar-nav {
  flex-direction: row;
}
.section2-img {
  background-image: url(/assets/images/bg.png);
  background-position: center;
  background-repeat: no-repeat;
  text-align: center;
}
.section2-img img {
  width: 70%;
  padding-bottom: 30px;
  padding-top: 60px;
  object-fit: scale-down;
}
.section2-content {
  text-align: center;
  display: grid;
  align-items: center;
  align-content: center;
}
.section2-content h2 {
  font-family: "EB Garamond", serif;
  font-size: 50px;
  font-weight: 700;
}
.hr hr {
  width: 60%;
}
.hr {
  display: flex;
  place-content: center;
}
.section2-content p {
  font-weight: 500;
  font-size: 18px;
  padding-top: 20px;
}
.management {
  background-image: url(/assets/images/management-bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  text-align: center;
  margin-top: -10px;
  padding: 70px 0;
}
.management h1 {
  font-weight: 700;
  font-size: 40px;
}
.management p {
  font-size: 18px;
  padding-top: 30px;
  padding-right: 50px;
  padding-left: 50px;
  font-weight: 500;
  padding-bottom: 40px;
}

.lab img {
  width: 95%;
  transition: 0.4s all;
  cursor: zoom-in;
}

.lightbox {
  display: none;
  position: fixed;
  z-index: 999;
  padding-top: 150px;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.9);
}

.lightbox-content {
  margin: auto;
  display: block;
  height: 500px;
}

.close {
  position: absolute;
  top: 20px;
  right: 35px;
  color: #fff;
  font-size: 40px;
  font-weight: bold;
  transition: 0.3s;
}

.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}

.prev {
  left: 15%;
}

.next {
  right: 15%;
}

.prev:hover,
.next:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.lab img:hover {
  transform: scale(1.05);
}

.lab {
  height: 160px;
  margin-bottom: 10px;
  border-radius: 10px;
  width: 100%;
  overflow: hidden;
  padding-bottom: 20px;
}

li {
  list-style: none;
}

.content {
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 300ms;
  box-shadow: 0px 0px 3px 1px #f68484ee;
  border-radius: 5px;
}

.front,
.back {
  background-color: #fff;
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  border-radius: 5px;
  overflow: hidden;
}

.back {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.back::before {
  position: absolute;
  content: " ";
  display: block;
  width: 160px;
  height: 160%;
  background: linear-gradient(
    90deg,
    transparent,
    #ff9966,
    #ff9966,
    #ff9966,
    #ff9966,
    transparent
  );
  animation: rotation_481 5000ms infinite linear;
}

.back-content {
  position: absolute;
  width: 99%;
  text-align: center;
  height: 99%;
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  color: white;
  align-items: center;
}
.faculty h3 {
  color: #000;
  padding-top: 25px;
}

@keyframes rotation_481 {
  0% {
    transform: rotateZ(0deg);
  }

  0% {
    transform: rotateZ(360deg);
  }
}

.front {
  transform: rotateY(180deg);
  color: white;
}

.front .front-content {
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.front-content .badge {
  background-color: #00000055;
  padding: 2px 10px;
  border-radius: 10px;
  backdrop-filter: blur(2px);
  width: fit-content;
}

.description {
  box-shadow: 0px 0px 10px 5px #00000088;
  width: 100%;
  padding: 10px;
  background-color: #00000099;
  backdrop-filter: blur(5px);
  border-radius: 5px;
}

.title {
  font-size: 11px;
  max-width: 100%;
  justify-content: space-between;
}

.title p {
  width: 50%;
}

.card-footer {
  color: #ffffff88;
  margin-top: 5px;
  font-size: 8px;
}

.front .img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.circle {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  position: relative;
  animation: floating 2600ms infinite linear;
}

.faculty p {
  margin-bottom: 18px !important;
}

.faculty a {
  padding: 10px 20px;
  background-color: #ef1c23;
  border-radius: 5px;
  font-size: 12px;
  color: #fff;
  transition: 0.5s all;
  text-decoration: none;
}

.faculty a:hover {
  background-color: rgba(255, 231, 231, 1) !important;
  border: 1.5px solid #ef1c23;
  color: #ef1c23 !important;
}

#bottom {
  background-color: #ff8866;
  left: 50px;
  top: 0px;
  width: 150px;
  height: 150px;
  animation-delay: -800ms;
}

#right {
  background-color: #ff2233;
  left: 160px;
  top: -80px;
  width: 30px;
  height: 30px;
  animation-delay: -1800ms;
}

@keyframes floating {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(10px);
  }

  100% {
    transform: translateY(0px);
  }
}
.tab-content {
  background-color: #fff;
}

.tab-content li:before {
  content: "\f19d` ";
  font-family: "FontAwesome";
  float: left;
  margin-left: -1.7em;
  color: rgba(239, 28, 35, 1);
}

.carousel-control-prev {
  background-color: rgba(239, 28, 35, 1) !important;
  height: 15%;
  width: 4.7% !important;
  border-radius: 50%;
  top: 30% !important;
  /* display: none !important; */
  left: 4% !important;
}
.carousel-control-next {
  background-color: rgba(239, 28, 35, 1) !important;
  height: 15%;
  width: 4.7% !important;
  border-radius: 50%;
  top: 30% !important;
  /* display: none !important; */
  right: 4% !important;
}

.staff {
  display: flex;
}
.staff-card {
  height: 100%;
  text-align: center;
  position: relative;
}
.Polygon1,
.Polygon2,
.Polygon3,
.Polygon4 {
  transition: 0.4s all;
}
.card1:hover .Polygon1 {
  transform: rotate(10deg);
}
.card2:hover .Polygon2 {
  transform: rotate(10deg);
}
.card3:hover .Polygon3 {
  transform: rotate(10deg);
}
.card4:hover .Polygon4 {
  transform: rotate(10deg);
}
.student-testi-content span {
  font-weight: 500;
  font-size: 13px;
}

.staff-img {
  position: absolute;
  left: 60px;
  object-fit: contain;
  top: 20px;
}
.staff-card h4 {
  font-size: 20px;
  font-weight: 700;
  padding: 50px 0 0 0;
}
.small,
small {
  font-size: 12px !important;
}
.staff-card h6 {
  padding-top: 10px;
  font-size: 16px;
}
.circularm {
  background-image: url(/assets/images/circular-bg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  text-align: center;
  padding: 70px 0;
  position: relative;
  z-index: 9;
}
.circulam-div h1 {
  color: #fff;
  font-size: 40px;
  font-weight: 700;
}
.circulam-div p {
  color: #fff;
  font-size: 18px;
  padding-top: 30px;
  padding-bottom: 35px;
}
.tab-pane {
  border-radius: 20px;
}

.vector {
  position: relative;
  top: -60px;
  left: 23%;
}
.vector img {
  position: absolute;
  width: 65%;
}
.occation-div {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}
.occations {
  padding: 30px;
  height: 620px;
  overflow-y: scroll;
  border-radius: 10px;
  width: 940px;
  float: left;
  background-color: #fff;
}

#style-3::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

#style-3::-webkit-scrollbar {
  width: 8px;
  background-color: #f5f5f5;
}

#style-3::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #d62929;
}

.fa-facebook-f,
.fa-linkedin-in,
.fa-instagram {
  font-size: 25px;
  color: #ef1c23;
  background-color: #fff;
  padding: 10px;
  border-radius: 5px;
  margin-left: 8px;
}
.main-menu ul li.has-dropdown > a::after {
  margin-left: 0px !important;
}
.tab-main {
  display: flex;
}
.flex-column {
  flex-direction: column !important;
  background-color: #fff;
  padding: 20px 50px 30px 0px;
  border-radius: 10px;
  margin-right: 40px;
}
.nav-pills .nav-link {
  margin: 5px 0;
  background: #eee !important;
}
.nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 30px !important;
  margin-bottom: 0;
  list-style: none;
}
.occations p {
  padding: 5px 25px;
  font-size: 18px;
  display: inline-block;
  background-color: rgba(230, 31, 38, 1);
  color: #fff;
  border-radius: 10px;
}
.occations span {
  font-weight: 800;
}
.calendar {
  display: flex;
  align-items: center;
  gap: 20px;
}
.calendar h4 {
  color: #000;
  font-size: 20px;
}
.fa-download {
  font-size: 30px;
  color: rgba(190, 190, 190, 1);
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: rgba(255, 0, 0, 1) !important;
  background-color: rgba(255, 231, 231, 1) !important;
  font-weight: 600;
}
.nav-pills .nav-link {
  color: black !important;
  font-weight: 600;
}
.nav-link {
  padding: 15px 10px;
}
.Associates {
  padding: 70px 0 200px 0px;
  text-align: center;
  position: relative;
}
.Associates h1 {
  font-size: 40px;
  font-weight: 700;
  padding-top: 40px;
  color: #000;
}
.main-card {
  display: flex;
  justify-content: center;
}
.Associates p {
  font-size: 18px;
  padding: 30px 0;
}
.review-card {
  position: relative;
  display: flex;
  justify-content: space-around;
  background-color: rgba(204, 24, 30, 1);
  border-radius: 10px;
  padding: 60px 30px 45px 30px;
  align-items: center;
  width: 80%;
}

.company-logo3 li::before,
.company-logo4 li::before {
  content: "";
  position: absolute;
  top: 21%;
  width: 2px;
  height: 82%;
  left: 56%;
  background-color: #b5b5b5;
  /* Keep pseudo-elements static */
}
.company-logo li::before,
.company-logo1 li::before {
  content: "";
  position: absolute;
  height: 82%;
  top: 11%;
  width: 2px;
  left: 56%;
  background-color: #b5b5b5;
}
.review p {
  padding: 0px 0;
  color: #fff;
}
.review h2 {
  font-weight: 700;
  color: #fff;
}
.student-group {
  background-image: url(/assets/images/student-group.png);
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  padding: 400px 0;
  position: relative;
  z-index: 9;
}
.broucher {
  background-image: url(/assets/images/brochers-bg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 120px 0 120px 0;
}
.broucher h1 {
  font-size: 40px;
  font-weight: 700;
  color: #000;
}
.broucher p {
  color: #000;
  font-size: 18px;
  line-height: 35px;
  padding: 30px 100px 0 0px;
}
.broucher-second {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}
.broucher-card {
  background-color: #fff;
  padding: 20px 20px 40px 20px;
  border-radius: 15px;
  box-shadow: 23px 33px 33px 0px rgba(0, 0, 0, 0.1);
}
.broucher-card h4 {
  font-size: 24px;
  color: #000;
  font-weight: 600;
  padding-top: 20px;
}
.next-btn {
  text-align: end;
}
.fa-arrow-right-long {
  color: #fff;
  padding: 15px;
  background-color: rgba(255, 0, 0, 1);
  border-radius: 50%;
}
/* .test-img {
  background-image: url(/assets/images/image\ 8.png);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  padding: 170px;
  border-radius: 160px 0px 160px 160px;
} */
.testimonials {
  text-align: center;
  background-color: rgba(242, 242, 242, 1);
  padding: 70px 0 130px 0;
}
.testimonials h1 {
  font-size: 40px;
  font-weight: 700;
  color: #000;
}
.testi-tab {
  display: inline-block;
  padding-top: 40px;
}
.testi-tab a {
  text-decoration: none;
}
.Student {
  background-color: rgba(239, 28, 35, 1);
  padding: 10px 40px;
  color: #fff;
  border-radius: 15px 0 0px 15px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}
.Student:hover {
  color: #fff;
}
.Companies:hover {
  color: #000;
}

/* Add transitions for smooth color and display changes */
#Student,
#Companies {
  transition: background-color 0.3s ease, color 0.3s ease;
}

#Students-slider,
#Companies-slider {
  transition: opacity 0.3s ease;
}

.Companies {
  padding: 10px 40px;
  color: #000;
  border-radius: 0px 15px 15px 0px;
  margin-left: -6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 16px;
}
.student-slide {
  background-color: #fff;
  width: 70%;
  position: relative;
  z-index: 2;
  padding: 60px 50px;
  border-radius: 15px;
}
.student-slide h3 {
  text-align: center;
  padding-bottom: 25px;
  font-weight: 700;
  font-size: 25px;
}
.occations h3 {
  font-size: 23px;
}
.occations hr {
  margin: 15px 0 !important;
}

.student-testi,
.student-testi1 {
  display: flex;
  display: none;
  gap: 30px;
  justify-content: center;
  text-align: justify;
}
.student-testi.active {
  display: flex;
}
.student-testi1.active {
  display: flex;
}
.vector1 {
  position: absolute;
  z-index: 9;
  left: 160px;
  margin-top: -590px;
  animation: floatAnimation 2s infinite alternate;
}
@keyframes floatAnimation {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-20px);
  }
}
.vector2 {
  position: absolute;
  margin-top: 550px;
  right: 171px;
  animation: floatAnimation 2s infinite alternate;
}

.student-testi p {
  font-size: 18px;
  line-height: 29px;
  padding-top: 10px;
  font-weight: 500;
  text-align: left;
  color: #000;
}
.student-testi1 p {
  font-size: 18px;
  line-height: 29px;
  padding-top: 10px;
  font-weight: 500;
  text-align: justify;
  color: #000;
}
.student-testi h6 {
  font-weight: 600;
  text-align: justify;
  padding-top: 20px;
  font-size: 16px;
}
.student-testi1 h6 {
  font-weight: 600;
  text-align: justify;
  padding-top: 20px;
  font-size: 16px;
}

.main-slide {
  display: grid;
  place-items: center;
  margin-top: 60px;
}

.fa-arrow-right {
  padding: 8px 20px;
  background-color: rgba(255, 233, 233, 1);
  border-radius: 10px;
  font-size: 20px;
  color: rgba(255, 0, 0, 1);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.16);
  margin-left: 10px;
}

.arrow i {
  cursor: pointer;
}
.fa-arrow-left {
  padding: 8px 20px;
  background-color: rgba(255, 233, 233, 1);
  border-radius: 10px;
  font-size: 20px;
  color: rgba(255, 0, 0, 1);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.16);
}
.arrow {
  text-align: end;
}
.logo1 p {
  margin-bottom: 0px !important;
}
.logo2 p {
  margin-bottom: 0px !important;
}
.logo1 {
  display: flex;
  align-items: center;
  gap: 20px;
}
.sub-footer {
  background-color: rgba(239, 28, 35, 1);
  padding: 10px 0;
}
.logo2 {
  display: flex;
  align-items: center;
  gap: 20px;
}
.sub-footer-con {
  display: flex;
  justify-content: space-around;
}
.sub-footer p {
  color: #fff;
  font-weight: 600;
  font-size: 18px;
}
footer {
  background-color: #000000fc;
  padding: 80px 0;
}

footer h2 {
  font-size: 23px;
  padding-bottom: 20px;
  font-weight: 600;
  color: #fff;
}

footer h2::before {
  content: "";
  height: 3px;
  width: 70px;
  border-radius: 10px;
  margin-top: 34px;
  position: absolute;
  background-color: rgba(239, 28, 35, 1);
}
.footer-menu a {
  text-decoration: none;
  transition: 0.6s all;
}
.footer-menu p {
  color: #fff;
  font-weight: 250;
  transition: 0.7s all;
}

footer p {
  color: #fff;
}
.footer-div2 {
  display: flex;
  justify-content: space-evenly;
}

.main-footer {
  display: flex;
  justify-content: space-around;
}
.footer-contact {
  display: flex;
  gap: 30px;
  align-items: center;
  justify-content: center;
  padding: 50px 0 0;
}
.main-menu ul li {
  margin-inline-end: 0px !important;
}
.location p {
  font-size: 16px;
}
.location {
  padding-top: 20px;
  display: flex;
  justify-content: center;
  margin-left: -60px;
  align-items: baseline;
  gap: 5px;
}
.fa-location-dot {
  font-size: 20px;
  color: #fff;
}
.call {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-top: 10px;
  padding-bottom: 20px;
}
.mail {
  display: flex;
  align-items: center;
  gap: 20px;
}
.fa-phone-volume,
.fa-envelope {
  font-size: 35px;
  color: rgba(201, 0, 0, 1);
}
.copyright {
  background-color: rgba(239, 28, 35, 1);
  padding: 30px 0 20px 0;
}
.copyright p {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}
.copyright-footer {
  display: flex;
  justify-content: space-evenly;
}
.copright-image img {
  padding-left: 15px;
}
.second-navbar {
  padding-top: 20px;
}
.Associates img {
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  border-radius: 50%;
}

.company-logo li,
.company-logo1 li {
  list-style: none;
  line-height: 27rem;
}
.company-logo3 li,
.company-logo4 li {
  list-style: none;
  line-height: 33rem;
}
.header-logo {
  max-width: 100% !important;
}
.main-menu ul li:hover > a::after {
  color: #fff !important;
}
.main-menu ul li .submenu li:hover > a {
  background-color: #000 !important;
  color: #fff !important;
}
.company-logo {
  position: absolute;
  left: 4%;
  margin-top: -320px;
}
#carouselExampleSlidesOnly {
  margin-top: -140px;
  padding-top: 85px;
}
.main-occations-div {
  height: 500px;
}
.sticky-social img:hover {
  transform: scale(1.1); /* Adjust the scale factor as needed */
}

.subnav,
.subnav1,
.subnav2,
.subnav3,
.subnav4 {
  margin-left: 20px !important;
  padding: 10px !important;
  border: 1px solid #eee !important;
}

.company-logo1 {
  position: absolute;
  right: 4%;
  margin-top: -320px;
}
.company-logo3 {
  position: absolute;
  right: 18%;
  margin-top: -250px;
}
.company-logo4 {
  position: absolute;
  left: 18%;
  margin-top: -250px;
}
.test-img img {
  width: 50px;
  position: absolute;
  top: 140px;
  left: 45px;
}
.logo-mob {
  display: none;
}
.header-logo img {
  width: 150px;
}
.management-mob {
  display: none;
}
.offcanvas__social ul li a {
  color: #ef1c23 !important;
}

.circle {
  position: absolute;
  top: 10%;
  left: 50px;
  animation: floatAnimation 1s ease-in-out infinite alternate;
}
.circle1 {
  position: absolute;
  top: 45%;
  left: 50px;
  animation: floatAnimation1 1s ease-in-out infinite alternate;
}
.circle2 {
  position: absolute;
  top: 80%;
  left: 50px;
  animation: floatAnimation2 1s ease-in-out infinite alternate;
}

.circle3 {
  position: absolute;
  top: 10%;
  right: 50px;
  animation: floatAnimation 1s ease-in-out infinite alternate;
}
.circle4 {
  position: absolute;
  top: 45%;
  right: 50px;
  animation: floatAnimation1 1s ease-in-out infinite alternate;
}
.circle5 {
  position: absolute;
  top: 80%;
  right: 50px;
  animation: floatAnimation2 1s ease-in-out infinite alternate;
}
.footer-logo {
  width: 35%;
}
.subcircle {
  position: absolute;
  top: 16%;
  left: 20%;
  animation: floatAnimation 1s ease-in-out infinite alternate;
}
.subcircle1 {
  position: absolute;
  top: 80%;
  left: 20%;
  animation: floatAnimation 1s ease-in-out infinite alternate;
}
.subcircle2 {
  position: absolute;
  top: 16%;
  right: 20%;
  animation: floatAnimation 1s ease-in-out infinite alternate;
}
.subcircle3 {
  position: absolute;
  top: 80%;
  right: 20%;
  animation: floatAnimation 1s ease-in-out infinite alternate;
}

@keyframes floatAnimation {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(0, -15px);
  }
}

@keyframes floatAnimation1 {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(0, 15px);
  }
}
@keyframes floatAnimation2 {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(0, -20px);
  }
}

.related-pages {
  height: 130px;
  width: 280px;
  border-left: 1px solid rgba(255, 111, 111, 1);
  border-right: 1px solid rgba(255, 111, 111, 1);
  border-top: 1px solid rgba(255, 111, 111, 1);
  border-bottom: 5px solid rgba(255, 111, 111, 1);
  border-radius: 10px;
  display: flex;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  justify-content: center;
  align-items: center;
  padding-top: 17px;
  transition: 0.8s all;
}
.related-pages:hover {
  box-shadow: rgb(255, 111, 111, 1) 3px 3px 6px 0px inset,
    rgba(255, 111, 111, 1) -3px -3px 6px 1px inset;
}
.pb-30 a {
  text-decoration: none;
}
.pb-30 {
  padding: 70px 0;
  text-align: center;
  background-image: url(/assets/images/department/be-cs/related-page-bg.png);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.pb-30 h2 {
  margin-bottom: 20px;
}

/* Preloader styles */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.dot-loader {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preloader img {
  width: 10%;
  padding-bottom: 15px;
}

.dot {
  width: 15px;
  height: 15px;
  background-color: #fff;
  border-radius: 50%;
  margin: 0 3px;
  animation: bounce 0.8s ease-in-out infinite alternate;
}

.dot:nth-child(2) {
  animation-delay: 0.2s; /* Shorter delay for the second dot */
}

.dot:nth-child(3) {
  animation-delay: 0.4s; /* Shorter delay for the third dot */
}
.dot:nth-child(4) {
  animation-delay: 0.6s; /* Shorter delay for the third dot */
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}

/*  */

@media (max-width: 600px) {
  .navbar-expand-lg .navbar-nav {
    flex-direction: column;
    margin-left: 0px;
  }
  .logo-mob {
    display: block;
  }
  .logo-desk {
    display: none;
  }
  .pages {
    display: flex !important;
    justify-content: center !important;
  }
  .related-pages {
    margin-bottom: 30px;
  }
  .pb-30 {
    padding: 40px 0 !important;
  }
  .header-logo h3,
  .header-logo span {
    display: none;
  }
  .offcanvas__info.info-open {
    z-index: 9999 !important;
  }
  #carouselExampleSlidesOnly {
    padding-top: 130px;
  }
  #header-sticky {
    padding: 5px 25px 5px 0 !important;
  }
  .management-desk {
    display: none;
  }
  .footer-logo {
    width: 100%;
  }
  .preloader img {
    width: 85% !important;
    padding-bottom: 25px !important;
  }
  .flex-column {
    margin-right: 0px !important;
  }
  .maintopmenu {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
  .sticky-social {
    right: -30px;
  }
  .sticky-social img {
    width: 60%;
  }
  .management-mob {
    display: block !important;
  }
  .bar-icon span:nth-child(2) small {
    background-color: #fff !important;
  }
  .bar-icon span {
    width: 100%;
    height: 2px;
    background: #fff !important;
    display: inline-block;
  }
  .carousel-item img {
    height: 50vh;
    object-fit: contain;
  }
  .second-navbar {
    padding-top: 0px;
  }
  .company-logo,
  .company-logo1,
  .company-logo3,
  .company-logo4 {
    display: none;
  }
  .sliderr img {
    height: 60% !important;
  }
  .nav-pills .nav-link {
    margin: 5px 0;
    background: none !important;
  }
  .navbar-expand-lg .navbar-collapse {
    position: absolute;
  }
  .hamburger-btn {
    display: block !important;
  }
  .logo-and-menu {
    padding-left: 0px !important;
  }
  .header-logo img {
    width: 80% !important;
  }
  .nav-pills {
    height: auto;
  }
  .header-left {
    justify-content: center !important;
    gap: 10px !important;
  }
  .topmenu-btn button {
    padding: 6px 6px !important;
    font-size: 10px;
  }
  .occations p {
    font-size: 13px !important;
  }
  .occation-div {
    gap: 7px;
  }
  .sticky-social {
    width: 17%;
  }
  .fa-facebook-f {
    font-size: 12px;
    color: #ef1c23;
    background-color: #0000;
    margin-left: 0px;
  }
  .fa-download {
    font-size: 20px;
  }
  .offcanvas__social {
    margin-bottom: 30px;
    margin-top: 30px;
  }
  .navbar-expand-lg .navbar-collapse {
    position: absolute;
    padding: 5px;
    right: 56px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    border-radius: 10px;
    top: 75px;
    background: white;
  }
  .navbar-light .navbar-nav .nav-link {
    color: black !important;
    font-weight: 600;
  }

  .container .card .content-box h2 {
    font-size: 23px;
    margin-bottom: 10px;
  }

  .lightbox-content {
    width: 95% !important;
    object-fit: contain !important;
  }

  .lab {
    margin-top: 20px !important;
  }

  .container .card .content-box span {
    display: block !important;
  }

  .container .card .content-box {
    height: 65px;
  }

  .container .card:hover .content-box {
    height: 280px;
  }
  .row-two {
    padding-top: 43px !important;
  }
  .top-menu {
    margin-top: 20px;
  }
  .logo-con {
    padding-top: 0px !important;
  }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding: 10px 20px !important;
  }
  .offcanvas__logo a img {
    width: 180px !important;
  }
  .sub-topmenu {
    display: flex;
    gap: 15px;
  }
  .navbar-toggler {
    padding: 2px !important;
  }

  .navbar-light .navbar-nav .nav-link {
    font-family: "Montserrat";
  }
  .header__hamburger {
    margin-top: -50px !important;
  }
  .section2-content br {
    display: none !important;
  }
  .Associates br {
    display: none !important;
  }
  .top-menu {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: 10px;
  }
  .video-section {
    padding: 30px 0 0 0;
  }
  .sub-topmenu h6 {
    color: #fff;
    font-size: 11px;
  }
  .navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='white' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
    background-size: 80% 80%;
  }
  .navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, 0.55);
    border-color: white !important;
  }
  .footer-menu {
    margin-bottom: 15px;
  }
  .main-footer {
    display: block;
  }
  .navbar-toggler:focus {
    box-shadow: "none" !important;
  }
  .logo {
    width: 60% !important;
  }
  .hr {
    display: block !important;
  }
  .hr hr {
    width: 100%;
  }
  .section2-content p {
    padding-bottom: 20px;
  }
  .section2-content h2 {
    padding-top: 20px;
    font-size: 40px;
  }
  .management h1 {
    font-size: 28px;
  }
  .management {
    padding: 50px 0;
  }
  p {
    font-size: 16px !important;
  }
  .management p {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .staff-card {
    width: 100%;
    height: auto;
    margin-bottom: 30px;
  }
  .occations hr {
    margin-bottom: 20px !important;
  }
  .staff {
    display: block;
  }
  .staff-img {
    left: 90px;
  }
  .carousel-control-next,
  .carousel-control-prev {
    height: 10%;
    width: 9% !important;
    display: block !important;
  }
  .Polygon1,
  .Polygon2,
  .Polygon3,
  .Polygon4 {
    margin-left: -40px;
  }
  .tab-main {
    display: block !important;
  }
  .nav {
    padding-left: 15px !important;
  }
  .flex-column {
    padding: 20px 20px 20px 0px;
    margin-right: 0px !important;
  }
  .occations {
    padding: 15px;
    margin-top: 40px;
    width: 100%;
  }
  .calendar h4 {
    color: #000;
    font-size: 13px;
  }
  .calendar {
    gap: 10px;
  }
  .review h2 {
    font-size: 20px;
  }
  .review p {
    font-size: 13px !important;
  }
  .Associates h1 {
    font-size: 28px;
    padding-top: 0px;
  }
  .Associates {
    padding: 40px 0px;
  }
  .bannermain-img {
    height: 50vh;
  }
  .review-card {
    padding: 40px 5px 28px 5px;
    width: 100%;
    align-items: baseline !important;
  }
  .broucher-second {
    display: block;
  }
  .broucher-card img {
    width: 100%;
  }
  .broucher-card {
    margin-bottom: 30px;
    padding: 20px 20px 20px 20px;
  }
  .broucher {
    background-color: rgba(242, 242, 242, 1);
    padding: 50px 0 40px 0;
  }
  .testimonials h1 {
    font-size: 28px;
  }
  .broucher h1 {
    font-size: 28px;
  }
  .broucher p {
    padding: 0px 0 15px 0;
    line-height: 23px;
  }
  .testimonials {
    padding: 40px;
  }
  .testimonials {
    padding: 50px 0;
  }
  .student-slide {
    width: 100%;
    padding: 40px 20px;
  }
  /* .test-img img {
    height: 100%;
  } */
  .student-testi.active {
    display: grid;
  }
  .student-testi1.active {
    display: grid;
  }
  .sub-footer-con {
    display: block;
    padding-left: 10px !important;
  }
  .logo1 {
    padding-bottom: 20px;
  }
  .line {
    display: none;
  }
  .logo2 {
    gap: 10px;
  }
  footer {
    padding: 40px 0;
  }
  .student-testi1 p {
    line-height: 25px !important;
  }
  .Associates p {
    padding: 10px 5px !important;
  }
  .footer-contact {
    display: block;
    padding: 20px 0 0;
  }
  .footerline {
    display: none;
  }
  .location,
  .social {
    padding: 20px;
    margin-left: 0px;
  }
  .copyright-footer {
    display: block;
    text-align: center;
  }
  .vector1 {
    margin-top: -1159px;
    left: 0;
  }
  .student-slide h3 {
    text-align: center;
  }
  .student-group {
    padding: 100px 0;
    background-attachment: local;
  }
  .circulam-div h1 {
    font-size: 28px;
  }
  .circulam-div p {
    padding-top: 12px;
    padding-left: 5px;
    padding-right: 5px;
    padding-bottom: 35px;
  }
  .circularm {
    padding: 40px 0 730px 0;
  }
  .main {
    display: none;
  }
  .main1 {
    display: none;
  }
}

/* animation */
/* /*  */
.placement-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.placement-card:hover {
  transform: translateY(-5px);
}

.placement-details {
  padding: 15px;
  text-align: center;
  background: #fff;
}

.placement-details h5 {
  color: #333;
  margin-bottom: 5px;
}

.placement-details p {
  color: #666;
  margin-bottom: 0;
}