@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=EB+Garamond:ital,wght@0,400..800;1,400..800&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");

* {
  padding: auto;
  margin: 0;
  font-family: "Montserrat";
}

.banner img {
  height: 200px;
}
.banner {
  background-image: url(/assets/images/banner5.jpg);
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 200px 0 40px 0;
}
.banner::before {
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0px;
  height: 356px;
  position: absolute;
  background-color: rgb(0 0 0 / 73%);
}
.title {
  text-align: left;
  padding-left: 50px;
}
.title h2 {
  color: white;
  text-shadow: 1px 1px 2px black, 0 0 0.1em rgba(255, 0, 0, 1),
    0 0 0.1em rgba(255, 0, 0, 1);
  font-size: 50px;
  font-weight: 800;
  letter-spacing: 2px;
  margin: 0%;
  position: relative;
}
.title a {
  color: white;
  text-decoration: none;
}
.title a:hover {
  color: rgba(255, 0, 0, 1);
}
.title p {
  position: relative;
  color: white;
  padding-top: 20px;
}
.slider-div {
  padding: 50px 200px 200px 200px;
}
.main-content-area {
  padding: 70px 0;
}
.nav-pills .nav-link {
  width: 240px;
  border-radius: 10px !important;
  display: flex;
  justify-content: space-between;
}
.nav {
  border: 1px solid #e0e0e0;
}
.nav-link {
  padding: 13px 15px !important;
}
.flex-column {
  flex-direction: column !important;
  background-color: #fff;
  padding: 20px 30px 30px 0px;
  border-radius: 10px;
  margin-right: 40px;
}
.nav-pills .nav-link {
  margin: 10px 0;
}
.nav-pills {
  height: 100%;
}
.div {
  padding: 55px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  border-radius: 20px;
}
.head {
  font-size: 32px;
  font-weight: 600;
  padding-bottom: 30px;
  text-align: left;
  position: relative;
}

.head::before {
  background-color: rgba(239, 28, 35, 1);
  content: "";
  position: absolute;
  width: 10%;
  height: 3px;
  top: 68%;
  border-radius: 10px;
}

.about-us p {
  font-size: 16px;
  font-weight: 500;
  text-align: justify;
}
.contents {
  padding: 0 60px;
}
.about-uscard {
  position: relative;
  padding: 44px 60px;
  border: 0.5px solid #cbcbcb;
  box-shadow: 0px 4px 17.9px 0px #00000026;
  border-radius: 10px;
  margin-top: 60px;
}
.about-uscard h2 {
  font-size: 28px;
}
.about-uscard img {
  width: 7%;
  position: absolute;
  top: -40px;
  left: -34px;
}
.mob {
  display: none;
}

.carousel-control-prev {
  background-color: red !important;
  height: 50px !important;
  width: 50px !important;
  top: 50% !important;
  border-radius: 50% !important;
  left: 0px !important;
  display: block !important;
}
/* .tab-main{
    justify-content: center;
} */
.carousel-control-next {
  background-color: red !important;
  height: 50px !important;
  width: 50px !important;
  top: 50% !important;
  border-radius: 50% !important;
  right: 0px !important;
  display: block !important;
}
.carousel-item img {
  width: 100%;
  border-radius: 20px;
}
.carousel-control-next-icon,
.carousel-control-prev-icon {
  height: 5rem !important;
}
.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
  text-align: -webkit-center;
}

iframe {
  border-radius: 10px;
  padding-top: 10px;
}
.video-sec {
  background-image: url(/assets/images/brochers-bg.png);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 70px 0;
}

table {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
}
.btn-holder {
  position: sticky;
  top: 150px;
}

td,
th {
  border: 1px solid #000000;
  text-align: left;
  padding: 20px;
  font-size: 20px;
}
.td {
  text-align: center;
}

tr {
  border: 1px solid black;
}
table,
th,
td {
  border: 1px solid black !important;
}
td p {
  font-weight: 600;
}
td h2 {
  font-weight: 600;
}
.lab h3 {
  font-weight: 600;
  font-size: 30px;
  padding: 30px 0 10px 0;
  text-align: center;
}
.lab p {
  font-weight: 500;
  font-size: 22px;
  padding: 10px 0 30px 0;
  text-align: center;
}
.about-us li {
  font-size: 17px;
  font-weight: 500;
  padding: 10px 0;
}
.ul li {
  font-size: 17px;
  font-weight: 500;
  padding: 10px 0;
}

.slider {
  position: relative;
  overflow: hidden;
  /* Add any styles you need for the slider container */
}

.slides {
  display: flex;
  transition: transform 0.5s ease;
  /* Add any styles you need for the slides container */
}

.slides img {
  width: 100%;
  /* Add any styles you need for the images */
}

.prev {
  position: absolute;
  top: 50%;
  left: 0px;
  height: 50px;
  width: 50px;
  border-radius: 50%;
  color: #fff;
  font-size: 20px;
  transform: translateY(-50%);
  background-color: rgba(255, 0, 0, 0.825) !important;
  border: none;
  cursor: pointer;
  /* Add any styles you need for the buttons */
}
.next {
  position: absolute;
  top: 50%;
  height: 50px;
  width: 50px;
  font-size: 20px;
  color: #fff;
  border-radius: 50%;
  right: 0px;
  transform: translateY(-50%);
  background-color: rgba(255, 0, 0, 0.825) !important;
  border: none;
  cursor: pointer;
  /* Add any styles you need for the buttons */
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  pointer-events: none; /* Initially, the popup is hidden and not interactive */
  transition: opacity 0.3s ease, transform 0.3s ease; /* Transition for opacity and transform */
}

.popup.show {
  opacity: 1;
  pointer-events: auto; /* Show the popup and make it interactive */
}

.popup img {
  max-width: 90%;
  position: relative;
  top: 7%;
  max-height: 75%;
  border-radius: 5px;
  cursor: pointer;
}

.popup.show img {
  transform: scale(
    1
  ); /* Apply scaling effect to the image when the popup is shown */
}
.slider img {
  cursor: zoom-in;
}

@media (max-width: 600px) {
  .container-fluid {
    padding: 0 15px !important;
  }
  .div {
    padding: 10px !important;
  }
  td,
  th {
    padding: 8px !important;
    font-size: 16px !important;
  }
  iframe {
    height: 200px;
    width: 300px;
  }
  .video-sec {
    padding: 40px 0;
    background-position: left;
  }
  .nav {
    display: contents !important;
    width: 100%;
    overflow: hidden !important;
  }

  .align-items-start {
    border-radius: 10px;
    gap: 5px !important;
    background-color: rgba(255, 231, 231, 1) !important;
  }

  .nav-pills .nav-link {
    font-size: 14px !important;
    width: auto;
    gap: 10px;
    border-radius: 0 !important;
    margin-left: 5px !important;
    align-items: center;
  }
  .slider-div {
    padding: 0px !important;
  }
  .head {
    padding-top: 30px;
    font-size: 22px;
  }
  .contents {
    padding: 0 0px;
  }
  .nav-pills .nav-link {
    margin: 20px 0;
  }
  .head::before {
    width: 30%;
    top: 82%;
  }
  .lab h3 {
    font-size: 22px;
  }
  .lab p {
    padding: 10px 0 15px 0;
  }

  .btn-holder {
    display: flex;
    overflow-x: scroll;
    position: static;
  }
  .main-content-area {
    overflow-x: hidden;
  }
  .tab-main {
    display: grid !important;
  }
  .mob {
    display: block;
  }
  .desk {
    display: none;
  }
  .subnav {
    padding: 10px !important;
  }
  .nav-link.active {
    border-bottom: 2px solid rgba(239, 28, 35, 1) !important;
  }
  .nav-link {
    padding: 4px 10px !important;
  }
  .tab-pane {
    border-radius: 0px;
  }
  .title {
    text-align: center !important;
    padding: 0 !important;
  }
  .title h2 {
    font-size: 23px;
    padding: 0 10px;
  }
  .banner::before {
    height: 327px;
  }
  .main-content-area {
    padding: 40px 0;
  }
  .banner {
    padding: 145px 0 70px 0;
  }
  .about-uscard {
    padding: 30px 20px;
  }
  .about-uscard img {
    width: 21%;
    left: -17px;
  }
}

/* regulation page */

.card-title {
  margin-bottom: 0.5rem;
  font-size: 20px;
  font-family: "Montserrat";
}
.card {
  overflow: hidden;
}
.card-header {
  font-size: 20px;
  font-family: "Montserrat";
  font-weight: 600;
  background-color: rgba(255, 231, 231, 1) !important;
}
.card img {
  transition: 0.4s all;
}
.card img:hover {
  transform: scale(1.05);
}
.card a {
  background-color: rgba(239, 28, 35, 1);
  color: #fff;
  font-size: 15px;
  transition: 0.4s all;
}
.card a:hover {
  background-color: #fff;
  color: rgba(239, 28, 35, 1);
  border: 1.5px solid rgba(239, 28, 35, 1);
}
.pdf {
  margin-bottom: 50px;
  padding-top: 20px;
}
.pdf li {
  background-color: #fff;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  padding: 20px 35px;
  margin-bottom: 20px;
  display: inline-block;
  border-radius: 20px;
}
.fa-right-long {
  padding-left: 10px;
  animation: floatRight 1s infinite alternate;
  color: rgba(239, 28, 35, 1);
}

@media (max-width: 600px) {
  .pdf {
    margin-bottom: 20px;
    padding-top: 20px;
  }
  .clg-logo img {
    width: 60% !important;
  }
  .clg-logo{
    padding-top: 30px !important;
  }

  .form h2::before {
    width: 40% !important;
    top: 70.4% !important;
}
  .pdf li {
    padding: 20px 10px !important;
  }

  td a {
    padding: 10px 5px !important;
    font-size: 12px !important;
  }
  .regulations {
    padding: 0px 25px !important;
  }
}

@keyframes floatRight {
  0% {
    transform: translateX(-30%);
  }
  100% {
    transform: translateX(50%);
  }
}

.pdf span {
  color: rgba(239, 28, 35, 1);
  text-decoration: underline;
  font-weight: 600;
  margin-left: 20px;
}
.pdf li a {
  text-decoration: none;
}
.course-head {
  font-size: 22px;
  padding: 20px 0;
  font-weight: 600;
}
td a {
  background-color: rgba(239, 28, 35, 1);
  padding: 8px 15px;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  transition: 0.3s all;
}
td a:hover {
  background-color: rgba(255, 231, 231, 1) !important;
  color: rgba(239, 28, 35, 1);
}

/* form */
.form-group label {
  color: #000000;
  font-size: 18px;
  font-weight: 500;
  padding: 30px 0px 10px 0px;
}

.form-group span {
  color: red;
}

.form-group input {
  height: 50px;
  border-radius: 5px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.form-group select {
  width: 100%;
  height: 50px;
  border-radius: 5px;
  padding: 0px 20px;
  font-size: 16px;
  border: 1.5px solid #eee;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.clg-logo {
  text-align: center;
}

.clg-logo img {
  width: 80%;
  animation: floatAnimation 2s ease-in-out infinite alternate;
}

.form {
  padding-bottom: 70px;
}

.form h2 {
  font-size: 30px;
  padding: 40px 0px 8px 0px;
  border-bottom: 3px solid rgba(239, 28, 35, 1);
}

.submit {
  margin-top: 50px;
}

.submit button {
  background-color: rgba(239, 28, 35, 1);
  color: #fff;
  font-size: 16px;
  padding: 11px 40px;
  width: 100%;
  border-radius: 5px;
  transition: 0.4s all;
  font-weight: 500;
}

.submit button:hover {
  background-color: rgba(255, 231, 231, 1) !important;
  color: rgba(239, 28, 35, 1);
}
