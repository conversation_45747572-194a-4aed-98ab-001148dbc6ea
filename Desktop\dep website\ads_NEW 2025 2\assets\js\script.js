// JavaScript for testimonial slider
let currentSlideIndex = 0; // Track the current slide index

// Function to slide the testimonials backward
function slideBack() {
  const testimonials = document.querySelectorAll(".student-testi");
  const totalSlides = testimonials.length;

  // Hide the current active slide
  testimonials[currentSlideIndex].classList.remove("active");

  // Calculate the previous slide index
  currentSlideIndex--;
  if (currentSlideIndex < 0) {
    currentSlideIndex = totalSlides - 1;
  }

  // Show the previous slide
  testimonials[currentSlideIndex].classList.add("active");
}

// Function to slide the testimonials forward
function slideForward() {
  const testimonials = document.querySelectorAll(".student-testi");
  const totalSlides = testimonials.length;

  // Hide the current active slide
  testimonials[currentSlideIndex].classList.remove("active");

  // Calculate the next slide index
  currentSlideIndex++;
  if (currentSlideIndex >= totalSlides) {
    currentSlideIndex = 0;
  }

  // Show the next slide
  testimonials[currentSlideIndex].classList.add("active");
}
// slider

function scrollToTop() {
  document.body.scrollTop = 0; // For Safari
  document.documentElement.scrollTop = 280; // For Chrome, Firefox, IE and Opera
}

// Simulate content loading delay
window.onload = function () {
  // Hide preloader when the page is fully loaded
  var preloader = document.querySelector(".preloader");
  preloader.style.display = "none";
};

// Define a function to initialize each slider
function initializeSlider(sliderContainer) {
  let slideIndex = 0;
  const slides = sliderContainer.querySelectorAll(".slides img");

  function showSlide(n) {
    if (n < 0) {
      slideIndex = slides.length - 1;
    } else if (n >= slides.length) {
      slideIndex = 0;
    }

    slides.forEach((slide, index) => {
      if (index === slideIndex) {
        slide.classList.remove("hide");
      } else {
        slide.classList.add("hide");
      }
    });
    sliderContainer.querySelector(".slides").style.transform = `translateX(-${
      slideIndex * 100
    }%)`;
  }

  function prevSlide() {
    showSlide(--slideIndex);
  }

  function nextSlide() {
    showSlide(++slideIndex);
  }

  // Show image in a popup when clicked
  slides.forEach((slide, index) => {
    slide.addEventListener("click", () => {
      const popup = document.createElement("div");
      popup.classList.add("popup");
      const img = document.createElement("img");
      img.src = slide.src;
      img.alt = slide.alt;
      popup.appendChild(img);
      document.body.appendChild(popup);

      // Toggle show class to display popup with transition
      setTimeout(() => {
        popup.classList.add("show");
      }, 50);

      // Close popup when clicked outside the image
      popup.addEventListener("click", () => {
        popup.classList.remove("show");
        // Delay removal of the popup to allow transition to complete
        setTimeout(() => {
          popup.remove();
        }, 300);
      });
    });
  });

  showSlide(slideIndex);
  sliderContainer.querySelector(".prev").addEventListener("click", prevSlide);
  sliderContainer.querySelector(".next").addEventListener("click", nextSlide);
}

// Initialize each slider on the page
document.querySelectorAll(".slider").forEach(initializeSlider);

//

// JavaScript for testimonial slider
let currentSlideIndex1 = 0; // Track the current slide index

// Function to slide the testimonials backward
function slideBack1() {
  const testimonial1 = document.querySelectorAll(".student-testi1");
  const totalSlides1 = testimonial1.length;

  // Hide the current active slide
  testimonial1[currentSlideIndex1].classList.remove("active");

  // Calculate the previous slide index
  currentSlideIndex1--;
  if (currentSlideIndex1 < 0) {
    currentSlideIndex1 = totalSlides1 - 1;
  }

  // Show the previous slide
  testimonial1[currentSlideIndex1].classList.add("active");
}

// Function to slide the testimonial1 forward
function slideForward1() {
  const testimonial1 = document.querySelectorAll(".student-testi1");
  const totalSlides1 = testimonial1.length;

  // Hide the current active slide
  testimonial1[currentSlideIndex1].classList.remove("active");

  // Calculate the next slide index
  currentSlideIndex1++;
  if (currentSlideIndex1 >= totalSlides1) {
    currentSlideIndex1 = 0;
  }

  // Show the next slide
  testimonial1[currentSlideIndex1].classList.add("active");
}

// sliders
var studentSlider = document.getElementById("Student-slider");
var companySlider = document.getElementById("Companies-slider");

function showCompanies() {
  var student = document.getElementById("Student");
  var companies = document.getElementById("Companies");

  companies.style.backgroundColor = "rgba(239, 28, 35, 1)";
  companies.style.color = "#fff";
  student.style.backgroundColor = "rgba(242, 242, 242, 1)";
  student.style.color = "#000";
  studentSlider.style.display = "none";
  companySlider.style.display = "grid";
}

function showStudents() {
  var student = document.getElementById("Student");
  var companies = document.getElementById("Companies");

  companies.style.backgroundColor = "rgba(242, 242, 242, 1)";
  companies.style.color = "#000";
  student.style.backgroundColor = "rgba(239, 28, 35, 1)";
  student.style.color = "#fff";
  studentSlider.style.display = "grid";
  companySlider.style.display = "none";
}

var menu = document.getElementById("navbarDropdownMenuLinkSubmenu"); // Corrected ID
var submenu = document.getElementById("subMenu"); // Corrected ID
menu.addEventListener("click", function () {
  submenu.style.display = "flex"; // Changed display style to "block" for visibility
});

function toggleSubnav(icon) {
  var submenuButtons = document.querySelectorAll(".subnav");
  var arrowIcon = document.querySelector("#arrow-icon i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all other submenus, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav1(icon) {
  var submenuButtons = document.querySelectorAll(".subnav1");
  var arrowIcon = document.querySelector("#arrow-icon1 i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the current submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all submenus first, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav2(icon) {
  var submenuButtons = document.querySelectorAll(".subnav2");
  var arrowIcon = document.querySelector("#arrow-icon2 i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all submenus first (if needed), then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav3(icon) {
  var submenuButtons = document.querySelectorAll(".subnav3");
  var arrowIcon = document.querySelector("#arrow-icon3 i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all other submenus, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav4(icon) {
  var submenuButtons = document.querySelectorAll(".subnav4");
  var arrowIcon = document.querySelector("#arrow-icon4 i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all other submenus, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav5(icon) {
  var submenuButtons = document.querySelectorAll(".sub-sub");
  var arrowIcon = document.querySelector("#arrow-icon-sub i");

  // Check if any submenu is visible
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all other submenus, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav6(icon) {
  var submenuButtons = document.querySelectorAll(".subnav6");
  var arrowIcon = document.querySelector("#arrow-icon6 i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all other submenus, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function toggleSubnav7(icon) {
  var submenuButtons = document.querySelectorAll(".sub-sub1");
  var arrowIcon = document.querySelector("#arrow-icon-sub1 i");

  // Check if the current submenu is open
  var isOpen = Array.from(submenuButtons).some(
    (button) => button.style.display === "flex"
  );

  if (isOpen) {
    // If the submenu is open, close it
    submenuButtons.forEach(function (button) {
      button.style.display = "none";
    });
    arrowIcon.classList.remove("fa-minus");
    arrowIcon.classList.add("fa-plus");
  } else {
    // Close all other submenus, then open the current one
    closeAllSubmenus();
    submenuButtons.forEach(function (button) {
      button.style.display = "flex";
    });
    arrowIcon.classList.remove("fa-plus");
    arrowIcon.classList.add("fa-minus");
  }
}

function closeAllSubmenus() {
  // Close all subnav menus
  document
    .querySelectorAll(
      ".subnav, .subnav1, .subnav2, .subnav3, .subnav4, .subnav6, .sub-sub, .sub-sub1"
    )
    .forEach(function (submenu) {
      submenu.style.display = "none";
    });

  // Reset all arrow icons to plus
  document.querySelectorAll(".right-arrow i").forEach(function (icon) {
    icon.classList.remove("fa-minus");
    icon.classList.add("fa-plus");
  });
}
