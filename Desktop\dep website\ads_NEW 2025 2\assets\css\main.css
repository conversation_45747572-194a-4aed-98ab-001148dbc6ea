/*-----------------------------------------------------------------------------------

    Theme Name: Diango - Health Medical Center HTML5 Template
    Author: bugfixx
    Support: mailto:masu<PERSON><PERSON><PERSON><EMAIL>
    Description: Diango - Health Medical Center HTML5 Template
    Version: 1.0
	Developer: <PERSON><PERSON>

-----------------------------------------------------------------------------------

/************ TABLE OF CONTENTS ***************

	01. THEME DEFAULT CSS START
  	02. ANIMATION CSS START
	03. <PERSON><PERSON><PERSON><PERSON><PERSON>UN<PERSON> COLOR CSS START
	04. BR<PERSON><PERSON><PERSON>UMB CSS START
	05. BACKTOTOP CSS START
	06. <PERSON><PERSON><PERSON><PERSON>UM<PERSON> CSS START
	07. BUTTON CSS START
	08. <PERSON><PERSON><PERSON><PERSON> CSS START
	09. MEAN MENU CSS START
	10. OFFCANVAS CSS START
	11. PAGINATION CSS START
	12. PRELOADER CSS START
	13. SEARCH CSS START
	14. SECTION TITLE CSS START
	15. SHORT CODE CSS START
	16. SOCIAL CSS START
	17. SPACE CSS START
	18. TAB CSS START
	19. ABOUT CSS START
	20. ACCORDION CSS START
	21.	ADVANTAGE CSS START
	22.	APPOINTMENT CSS START
	23.	BANNER CSS START
	24.	BENEFIT CSS START
	25.	BLOG CSS START
	26.	BRAND CSS START
	27.	CONTACT CSS START
	28. COUNTER CSS START
	29. DISCOUNT CSS START
	30. DOCTOR CSS START
	31. ERROR CSS START
	32. FAQ CSS START
	33. FEATURES CSS START
	34. FEATURES CSS START
	35. FOOTER CSS START
	36. HEADER CSS START
	37. INTRO CSS START
	38. MENU CSS START
	39. NEWSLETTER CSS START
	40. PRODUCT DETAILS CSS START
	41. PRODUCT CSS START
	42. PROJECT CSS START
	43. SERVICE CSS START
	44. SKILL CSS START
	45. SLIDER CSS START
	46. SUPPORT CSS START
	47. TEAM CSS START
	48. TESTIMONIAL CSS START
	49. VACCINE CSS START
	50. WHAT WE DO CSS START
	51. WHY CHOOSE CSS START
	52. WORK CHOOSE CSS START

**********************************************/
/*----------------------------------------*/
/*  01. THEME DEFAULT CSS START
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Sora:wght@300;400;500;600;700&display=swap");
.text-color-1 {
  color: var(--clr-theme-primary);
}

.text-color-2 {
  color: var(--clr-theme-secondary);
}

.text-color-3 {
  color: var(--clr-theme-3);
}

.text-color-4 {
  color: var(--clr-theme-4);
}

.text-color-5 {
  color: var(--clr-theme-5);
}

.footer-link-2 ul li a::after,
.footer-style-4 .footer-link ul li a::after,
.contact__from-input textarea,
.cart-area .product-quantity-form,
.doctor-item,
.doctor-card,
.doctor-card-2,
.doctor-social-3,
.product-thumb img,
.product-tooltip,
.discount-thumb,
.brand-item,
.why-choose-item,
.why-choose-serial span,
.project-item,
.project-item:before,
.project-content-inner,
.blog-content-box,
.blog-item-3,
.blog-thumb-bg,
.blog-thumb-bg::after,
.team-content,
.service-item,
.service-thumb,
.service-icon span,
.service__thumb-2,
.service-bottom-content,
.service-item-3,
.service-thumb-4:before,
.service-link-2,
.bar-icon span:nth-child(2),
.bar-icon-2 span:nth-child(1),
.main-menu ul li.has-dropdown > a::after,
.main-menu ul li .submenu,
.main-menu ul li .mega-menu,
.main-menu ul li .mega-menu li ul li a::before,
.df-search-area,
.mean-container .mean-nav ul li > a > i,
.mean-container .mean-nav ul li a.mean-expand.mean-clicked i,
.offcanvas__contact-icon i,
.offcanvas__overlay,
.fill-btn.has-border i,
a,
.btn,
button,
span,
svg,
p,
input,
select,
textarea,
li,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6,
i[class^="flaticon-"],
.body__overlay {
  /* -webkit-transition: all 0.3s 0s ease-out; */
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  /* transition: all 0.3s 0s ease-out; */
}

/* transform */
:root {
  /**
  @font family declaration
  */
  --bd-ff-body: "Sora", sans-serif;
  --bd-ff-fontawesome: "Font Awesome 6 Pro";
  /**
  @color declaration
  */
  --clr-common-white: #fff;
  --clr-common-black: #000;
  --clr-common-placeholder: #808080;
  --clr-common-heading: #191919;
  --clr-common-body-text: #555555;
  --clr-common-selection: #262626;
  --clr-common-body: #fff;
  --clr-theme-primary: #02afe5;
  --clr-theme-secondary: #eb753b;
  --clr-text-primary: #fff;
  --clr-text-secondary: #161616;
  --clr-text-body: #555555;
  --clr-text-2: #222222;
  --clr-text-3: #9f9f9f;
  --clr-text-4: #808080;
  --clr-bg-1: #f0f0f0;
  --clr-bg-2: #191919;
  --clr-bg-3: #edf9fd;
  --clr-bg-4: #f2fbfe;
  --clr-bg-5: #f7f7f7;
  --clr-border-1: #2f2f2f;
  --clr-border-2: rgba(85, 85, 85, 0.2);
  --clr-gradient-1: linear-gradient(96.81deg, #ff589b 0%, #ffc107 100%);
  /**
  @font weight declaration
  */
  --bd-fw-normal: normal;
  --bd-fw-thin: 100;
  --bd-fw-elight: 200;
  --bd-fw-light: 300;
  --bd-fw-regular: 400;
  --bd-fw-medium: 500;
  --bd-fw-sbold: 600;
  --bd-fw-bold: 700;
  --bd-fw-ebold: 800;
  --bd-fw-black: 900;
  /**
  @font size declaration
  */
  --bd-fs-body: 16px;
  --bd-fs-p: 16px;
  --bd-fs-h1: 80px;
  --bd-fs-h2: 50px;
  --bd-fs-h3: 28px;
  --bd-fs-h4: 24px;
  --bd-fs-h5: 20px;
  --bd-fs-h6: 16px;
}

* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

html {
  font-size: 10px;
}

/*---------------------------------
    Typography css start 
---------------------------------*/
body {
  font-family: var(--bd-ff-body);
  font-size: var(--bd-fs-body);
  font-weight: normal;
  color: var(--clr-text-body);
  line-height: 26px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--bd-ff-body);
  color: var(--clr-text-secondary);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  font-weight: var(--bd-fw-sbold);
}

h1 {
  font-size: var(--bd-fs-h1);
}

h2 {
  font-size: var(--bd-fs-h2);
}

h3 {
  font-size: var(--bd-fs-h3);
}

h4 {
  font-size: var(--bd-fs-h4);
}

h5 {
  font-size: var(--bd-fs-h5);
}

h6 {
  font-size: var(--bd-fs-h6);
}

ul {
  margin: 0px;
  padding: 0px;
}

p {
  font-size: var(--bd-fs-p);
  font-weight: var(--bd-fw-normal);
  color: var(--clr-text-body);
  margin-bottom: 15px;
  line-height: 26px;
}

a {
  text-decoration: none;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  margin: 0;
  border-color: rgba(22, 22, 22, 0.1);
  opacity: 1;
  border-width: 1px;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="password"],
textarea {
  outline: none;
  background-color: #fff;
  height: 65px;
  width: 100%;
  line-height: 65px;
  font-size: 16px;
  color: var(--clr-common-heading);
  padding-left: 26px;
  padding-right: 26px;
  border: 1px solid #e0e2e3;
}

*::-moz-selection {
  background: var(--clr-common-selection);
  color: var(--clr-common-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--clr-common-selection);
  color: var(--clr-common-white);
  text-shadow: none;
}

::selection {
  background: var(--clr-common-selection);
  color: var(--clr-common-white);
  text-shadow: none;
}

*::-moz-placeholder {
  color: var(--clr-common-placeholder);
  opacity: 1;
  font-size: 16px;
}

*::placeholder {
  color: var(--clr-common-placeholder);
  opacity: 1;
  font-size: 16px;
}

/*----------------------------------------
   Flaction customize
-----------------------------------------*/
i[class^="flaticon-"] {
  line-height: 1;
  top: 2px;
  position: relative;
}

/*----------------------------------------
   Bootstrap customize
-----------------------------------------*/
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  --bs-gutter-x: 3rem;
}

.row {
  --bs-gutter-x: 3rem;
}

@media (min-width: 1441px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1350px;
  }
}
.custom-container {
  max-width: 1620px;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 40px;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 40px;
}

.g-50,
.gx-50 {
  --bs-gutter-x: 50px;
}

.g-50,
.gy-50 {
  --bs-gutter-y: 50px;
}

.g-60,
.gy-60 {
  --bs-gutter-y: 60px;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}

.g-15 {
  --bs-gutter-y: 15px;
  --bs-gutter-x: 15px;
}

/*---------------------------------
    common classes css start 
---------------------------------*/
.w-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.m-img img {
  max-width: 100%;
  height: 100%;
  object-fit: cover;
}

.fix {
  overflow: hidden;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.z-index-1 {
  z-index: 1;
}

.z-index-11 {
  z-index: 11;
}

.overflow-y-visible {
  overflow-x: hidden;
  overflow-y: visible;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

/*----------------------------------------
   Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--clr-common-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--bd-ff-fontawesome);
  font-size: 31px;
  font-weight: 200;
  right: -20px;
  margin-top: -25px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    right: 15px;
    margin-top: -30px;
  }
}

.body__overlay {
  background-color: var(--clr-common-heading);
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  left: 0;
  opacity: 0;
  visibility: hidden;
}

.body__overlay {
  background-color: var(--clr-common-heading);
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  left: 0;
  opacity: 0;
  visibility: hidden;
}

.body__overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

.bd-sticky {
  position: sticky;
  top: 120px;
}

/*----------------------------------------
    Body Overlay 
-----------------------------------------*/
.body-overlay {
  background-color: var(--clr-common-black);
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 999;
  left: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
.body-overlay:hover {
  cursor: url("../imgs/icons/cross-out.png"), pointer;
}

.body-overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

/*----------------------------------------*/
/*  BACKGROUND COLOR CSS START
/*----------------------------------------*/
/*--- Background color
-----------------------------------------*/
.white-bg {
  background-color: var(--clr-text-primary);
}

.black-bg {
  background-color: var(--clr-common-black);
}

.dark-bg {
  background-color: var(--clr-common-heading);
}

.grey-bg {
  background-color: var(--clr-bg-gray-1);
}

.footer-bg {
  background-color: var(--clr-bg-2);
}

.theme-bg-1 {
  background: var(--clr-theme-primary);
}

.theme-bg-2 {
  background: var(--clr-bg-1);
}

.theme-bg-3 {
  background: var(--clr-bg-2);
}

.theme-bg-4 {
  background: var(--clr-bg-3);
}

.theme-bg-5 {
  background: var(--clr-bg-4);
}

/*----------------------------------------*/
/*  BUTTON CSS START
/*----------------------------------------*/
.pulse-btn {
  display: inline-block;
  width: 80px;
  height: 80px;
  line-height: 80px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--clr-common-white);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  color: var(--clr-common-black);
  animation: pulse 2s infinite;
}
.pulse-btn:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}
.pulse-btn i {
  padding-left: 2px;
}

.fill-btn {
  font-size: 16px;
  font-weight: var(--bd-fw-medium);
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
  height: 60px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 35px;
  border-radius: 50px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: inline-flex;
  transition: 0.5s;
  text-transform: capitalize;
  gap: 5px;
}
.fill-btn:focus {
  color: var(--clr-common-white);
}
.fill-btn::before {
  content: "";
  background-color: var(--clr-theme-primary);
  height: 450px;
  width: 200%;
  border-radius: 50%;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  transition: 0.5s;
}
.fill-btn i {
  color: var(--clr-common-white);
}
.fill-btninner,
.fill-btn-hover,
.fill-btn-normal {
  pointer-events: none;
  display: flex;
  gap: 7px;
  align-items: center;
}
.fill-btn-inner {
  position: relative;
}
.fill-btn-hover {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transform: translateY(70%);
}
.fill-btn:hover {
  color: var(--clr-common-white);
}
.fill-btn:hover .fill-btn-normal {
  opacity: 0;
  transform: translateY(-70%);
  transition: transform 1.4s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.2s linear;
}
.fill-btn:hover .fill-btn-hover {
  opacity: 1;
  transform: translateY(0);
  transition: transform 1.4s cubic-bezier(0.19, 1, 0.22, 1),
    opacity 1.4s cubic-bezier(0.19, 1, 0.22, 1);
}
.fill-btn:hover::before {
  transform: translate(-50%, -80%);
}
.fill-btn.secondary:hover::before {
  background: var(--clr-text-secondary);
}
.fill-btn.has-border {
  background: transparent;
  color: var(--clr-theme-secondary);
  border: 1px solid var(--clr-theme-secondary);
}
.fill-btn.has-border i {
  color: var(--clr-theme-secondary);
}
.fill-btn.has-border::before {
  background-color: var(--clr-theme-secondary);
}
.fill-btn.has-border:hover {
  color: var(--clr-common-white);
  border-color: transparent;
}
.fill-btn.has-border:hover i {
  color: var(--clr-common-white);
}

.appointment__btn .fill-btn::before {
  background: var(--clr-theme-secondary);
}

.btn-wrapper {
  padding-top: 55px;
}
@media (max-width: 575px) {
  .btn-wrapper {
    padding-top: 45px;
  }
}

.link-text span img {
  max-width: 20px;
}
.link-text a:hover {
  color: var(--clr-theme-secondary);
}

.border-btn {
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--clr-theme-secondary);
  padding: 0 30px;
  border-radius: 30px;
  text-transform: capitalize;
  color: var(--clr-theme-secondary);
}
.border-btn:hover {
  color: var(--clr-common-white);
  background-color: var(--clr-theme-secondary);
  border-color: transparent;
}

.button-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px 20px;
}

.round-link {
  height: 60px;
  width: 60px;
  display: inline-flex;
  background: rgba(235, 117, 59, 0.2);
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border-radius: 50%;
  color: var(--clr-theme-secondary);
}
.round-link:hover {
  background: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}

.round-cercle {
  position: relative;
}
.round-cercle .icon-box {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  left: 50%;
  font-size: 26px;
  color: var(--clr-common-white);
  z-index: 5;
}
.round-cercle .image-text {
  height: 187px;
  width: 187px;
  padding: 10px;
  background: var(--clr-theme-primary);
  border-radius: 50%;
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .round-cercle .image-text {
    height: 160px;
    width: 160px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .round-cercle .image-text {
    height: 150px;
    width: 150px;
  }
}

.border__btn {
  height: 45px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--clr-border-1);
  padding: 0 15px;
  border-radius: 30px;
}

.btn__dot {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-theme-primary);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.theme__btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.btn__text {
  font-size: 16px;
  font-weight: var(--bd-fw-sbold);
  color: var(--clr-common-white);
}

.input__btn {
  font-size: 16px;
  color: var(--clr-common-white);
  font-weight: var(--bd-fw-sbold);
  background: #0d0d0d;
  padding: 0px 25px;
  padding: 0px 25px;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: -ms-inline-flexbox;
  align-items: center;
  justify-content: center;
  gap: 10px;
  height: 60px;
  position: relative;
  z-index: 5;
}
.input__btn i {
  font-size: 20px;
}

.solid-btn {
  font-size: 16px;
  font-weight: var(--bd-fw-medium);
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: inline-flex;
  transition: 0.5s;
  text-transform: capitalize;
  gap: 5px;
}
.solid-btn:hover {
  background: var(--clr-theme-primary);
  color: var(--clr-common-white);
}
.solid-btn:hover span {
  color: var(--clr-common-white);
}

.join-btn {
  font-size: 16px;
  font-weight: var(--bd-fw-medium);
  background: transparent;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: inline-flex;
  transition: 0.5s;
  text-transform: capitalize;
  gap: 5px;
  border: 1px solid var(--clr-common-black);
}
.join-btn:hover {
  background: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}
.join-btn:hover span {
  color: var(--clr-common-white);
}

.link-text span a {
  font-weight: 500;
  color: var(--clr-common-heading);
}
.link-text span a:hover {
  color: var(--clr-theme-secondary);
}

.video-play-btn {
  height: 120px;
  width: 120px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--clr-common-white);
  border-radius: 50%;
  font-size: 26px;
  background: var(--clr-theme-primary);
  animation: pulse 2s infinite;
  -webkit-animation: pulse 2s infinite;
}
@media (max-width: 575px) {
  .video-play-btn {
    height: 100px;
    width: 100px;
  }
}
.video-play-btn:hover {
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
}

.round-link-40 {
  height: 60px;
  width: 60px;
  display: inline-flex;
  background: var(--clr-common-white);
  align-items: center;
  justify-content: center;
  font-size: 20px;
  border-radius: 50%;
  color: var(--clr-text-secondary);
  border: 1px solid var(--clr-border-2);
}
.round-link-40:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}

.text-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  position: relative;
  font-weight: 500;
  text-transform: capitalize;
}
.text-btn:after {
  position: absolute;
  content: "";
  left: auto;
  bottom: 0;
  background: currentColor;
  width: 0;
  height: 1px;
  transition: 0.3s;
  right: 0;
}
.text-btn:hover {
  color: var(--clr-theme-secondary);
}
.text-btn:hover::after {
  width: 100%;
  left: 0;
  right: auto;
}

.link-text-white a {
  color: var(--clr-common-white);
}
.link-text-white a:hover {
  color: var(--clr-theme-secondary);
}

/*----------------------------------------*/
/* OFFCANVAS CSS START
/*----------------------------------------*/
.offcanvas__close button {
  height: 45px;
  width: 45px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--clr-common-white);
  font-size: 18px;
  background-color: var(--clr-theme-primary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}
.offcanvas__close button i {
  color: var(--clr-common-white);
}
.offcanvas__info {
  background: #191919 none repeat scroll 0 0;
  border-left: 2px solid var(--clr-theme-primary);
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.offcanvas__info ::-webkit-scrollbar {
  display: none;
}
@media (max-width: 450px) {
  .offcanvas__info {
    width: 300px;
  }
}
.offcanvas__info.info-open {
  opacity: 1;
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0);
}
.offcanvas__logo a img {
  width: 150px;
}
.offcanvas__wrapper {
  position: relative;
  height: 100%;
  padding: 30px 30px;
}
@media (max-width: 575px) {
  .offcanvas__wrapper {
    padding: 20px;
  }
}
.offcanvas__top {
  padding-bottom: 20px;
  border-bottom: 1px solid var(--clr-border-2);
}
.offcanvas__search {
  position: relative;
}
.offcanvas__search input {
  width: 100%;
  height: 50px;
  line-height: 40px;
  padding-right: 20px;
  background: transparent;
  border: none;
  outline: none;
  border-bottom: 1px solid var(--clr-border-2);
  font-size: 14px;
  color: var(--clr-common-white);
  padding-left: 0;
}
.offcanvas__search input::placeholder {
  color: var(--clr-common-white);
}
.offcanvas__search input:focus {
  border-color: var(--clr-common-white);
}
.offcanvas__search button {
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 16px;
  color: var(--clr-common-white);
}
.offcanvas__search button:hover {
  color: var(--clr-theme-primary);
}
.offcanvas__text p {
  margin-bottom: 25px;
}
.offcanvas__contact h4 {
  font-size: 22px;
  margin-bottom: 20px;
  font-weight: var(--bd-fw-bold);
  color: var(--clr-common-white);
}
.offcanvas__contact ul li:not(:last-child) {
  margin-bottom: 20px;
}
.offcanvas__contact ul li:hover i {
  color: var(--clr-common-white);
  border-color: var(--clr-theme-primary);
  background-color: var(--clr-theme-primary);
}
.offcanvas__contact-icon i {
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  text-align: center;
  border: 1px solid var(--clr-border-1);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  overflow: hidden;
  color: var(--clr-common-white);
  position: relative;
  z-index: 5;
}
.offcanvas__contact-text a {
  font-size: 16px;
  font-weight: 500;
  color: var(--clr-common-white);
}
.offcanvas__contact-text a:hover {
  color: var(--clr-theme-primary);
}
.offcanvas__social {
  margin-bottom: 30px;
}
.offcanvas__social ul li {
  display: inline-block;
}
.offcanvas__social ul li:not(:last-child) {
  margin-right: 5px;
}
.offcanvas__social ul li a {
  display: inline-block;
  width: 40px;
  height: 40px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--clr-border-1);
  color: var(--clr-theme-primary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  z-index: 5;
  background-color: transparent;
}
.offcanvas__social ul li a:hover {
  color: var(--clr-common-white);
  border: 1px solid var(--clr-theme-primary);
  background-color: var(--clr-theme-primary);
}
.offcanvas__notification-icon a {
  font-size: 14px;
  color: var(--clr-common-white);
  position: relative;
}
.offcanvas__notification-icon a .notification-count {
  position: absolute;
  top: -4px;
  right: -13px;
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 22px;
  font-size: 12px;
  font-weight: 600;
  background-color: var(--clr-theme-primary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  color: var(--clr-common-white);
  text-align: center;
}
.offcanvas__notification-text p {
  margin-bottom: 0;
  font-weight: 500;
  color: var(--clr-common-white);
  font-size: 14px;
}
.offcanvas__notification-text p .notification-number {
  color: var(--clr-theme-primary);
}
.offcanvas__notification-text p a {
  color: var(--clr-theme-primary);
}
.offcanvas__overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: var(--clr-common-black);
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  right: 0;
}
.offcanvas__overlay.overlay-open {
  opacity: 0.8;
  visibility: visible;
}

.sidebar__toggle {
  cursor: pointer;
}

@media (max-width: 992px) {
  .app__offcanvas-overlay.overlay-open {
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 10;
  }
}

.offcanvas__contact-icon {
  margin-inline-end: 15px;
}

/*----------------------------------------*/
/*  SHORT CODE CSS START 
/*----------------------------------------*/
.b-b {
  border-bottom: 1px solid var(--clr-border-1);
}

.b-t {
  border-top: 1px solid var(--clr-border-1);
}

.divider-border {
  border: 1px solid var(--clr-border-1);
  display: inline-block;
}

.text-color-1 {
  color: var(--clr-theme-primary);
}

.text-color-2 {
  color: var(--clr-theme-secondary);
}

/*----------------------------------------*/
/*  SECTION TITLE CSS START
/*----------------------------------------*/
.section-title-space {
  margin-bottom: 55px;
}

.section-subtitle span {
  background-color: rgba(3, 175, 229, 0.2);
  padding: 11px 20px 11px 34px;
  line-height: 1;
  display: inline-block;
  border-radius: 40px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 30px;
  position: relative;
}
.section-subtitle span:after {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 19px;
  background-color: var(--clr-common-white);
  border: 5px slid var(--clr-theme-secondary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.section-subtitle span::before {
  position: absolute;
  content: "";
  height: 11px;
  width: 11px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 16px;
  background-color: var(--clr-theme-primary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

.section-title {
  font-size: 52px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .section-title {
    font-size: 48px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .section-title {
    font-size: 42px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title {
    font-size: 45px;
  }
  .section-title br {
    display: none;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .section-title {
    font-size: 32px;
  }
  .section-title br {
    display: none;
  }
}
@media (max-width: 450px) {
  .section-title {
    font-size: 28px;
  }
}

.section__title-wrapper.is-black .section__subtitle {
  color: var(--clr-common-white);
}
.section__title-wrapper.is-black .section__title {
  color: #222222;
}
.section__title-wrapper.is-center .section__back-title {
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
}
.section__title-wrapper.is-left .section__subtitle,
.section__title-wrapper.is-left .section__title {
  margin-inline-start: 91px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .section__title-wrapper.is-left .section__subtitle,
  .section__title-wrapper.is-left .section__title {
    margin-inline-start: 0px;
  }
}
.section__title-inner {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 60px;
  align-items: center;
}

.section-title-wrapper.is-white .section-title {
  color: var(--clr-common-white);
}
.section-title-wrapper.is-white .section-subtitle span {
  color: var(--clr-common-white);
  background: rgba(255, 255, 255, 0.2);
}

.section-subtitle-2 {
  font-size: 16px;
  position: relative;
  padding-left: 45px;
  display: inline-block;
  text-transform: uppercase;
  color: var(--clr-theme-primary);
}
.section-subtitle-2:after {
  position: absolute;
  content: "";
  width: 32px;
  height: 12px;
  top: 50%;
  left: 0;
  background: linear-gradient(280deg, #02afe5 0%, rgba(2, 175, 229, 0) 100%);
  transform: translateY(-50%);
  border-radius: 30px;
}

.section-title-2 {
  font-size: 64px;
}

.section-title-wrapper-2.is-white .section-subtitle-2 {
  color: var(--clr-common-white);
}
.section-title-wrapper-2.is-white .section-subtitle-2:after {
  background: linear-gradient(-45deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
}

.section-subtitle-3 {
  display: inline-block;
  color: var(--clr-theme-primary);
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 2px;
  font-weight: 500;
}

.section-title-3 {
  font-size: 44px;
  line-height: 1.1;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .section-title-3 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title-3 {
    font-size: 38px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title-3 {
    font-size: 36px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .section-title-3 {
    font-size: 32px;
  }
}
@media (max-width: 450px) {
  .section-title-3 {
    font-size: 28px;
  }
}

.section-title-wrapper-3.is-white .section-subtitle-3 {
  color: var(--clr-common-white);
}
.section-title-wrapper-3.is-white .section-title-3 {
  color: var(--clr-common-white);
}
.section-title-wrapper-3.is-white > p {
  color: var(--clr-common-white);
}

.section-subtitle-4 {
  font-weight: 600;
  text-transform: uppercase;
  background: rgba(3, 175, 229, 0.15);
  padding: 2px 15px;
  font-size: 14px;
  display: inline-block;
  color: var(--clr-theme-primary);
}

.section-title-4 {
  font-size: 52px;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .section-title-4 {
    font-size: 45px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title-4 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .section-title-4 {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .section-title-4 {
    font-size: 36px;
  }
}
@media (max-width: 450px) {
  .section-title-4 {
    font-size: 32px;
  }
}

.section-title-wrapper-4.is-white .section-subtitle-4 {
  background: rgba(255, 255, 255, 0.15);
  color: var(--clr-common-white);
}
.section-title-wrapper-4.is-white .section-title-4 {
  color: var(--clr-common-white);
}

.section-subtitle-5 {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-transform: capitalize;
  border: 1px solid var(--clr-border-2);
  display: inline-block;
  padding: 5px 20px 5px 40px;
  border-radius: 30px;
  position: relative;
}

.section-subtitle-5:after {
  position: absolute;
  content: "";
  height: 100%;
  border-radius: 50%;
  width: 11px;
  height: 11px;
  transform-origin: 0 0;
  left: 18px;
  background: var(--clr-theme-secondary);
  top: 50%;
  transform: translateY(-50%);
}

.section-subtitle-6 {
  color: var(--clr-theme-primary);
  display: inline-block;
  font-weight: 500;
  text-transform: uppercase;
  padding: 8px 24px 6px;
  border: 1px solid rgba(3, 175, 229, 0.3);
  line-height: 1;
}

/*----------------------------------------*/
/*  SPACE CSS START
/*----------------------------------------*/
.section-space {
  padding-top: 120px;
  padding-bottom: 120px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}

.section-space-top {
  padding-top: 120px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space-top {
    padding-top: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-top {
    padding-top: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space-top {
    padding-top: 70px;
  }
}

.section-space-bottom {
  padding-bottom: 120px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space-bottom {
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-bottom {
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space-bottom {
    padding-bottom: 70px;
  }
}

.section-space-medium {
  padding: 100px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space-medium {
    padding: 80px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-medium {
    padding: 60px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space-medium {
    padding: 60px 0;
  }
}

.section-space-medium-top {
  padding-top: 100px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space-medium-top {
    padding-top: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-medium-top {
    padding-top: 60px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .section-space-medium-top {
    padding-top: 60px;
  }
}

.section-space-medium-bottom {
  padding-bottom: 100px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space-medium-bottom {
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-medium-bottom {
    padding-bottom: 60px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space-medium-bottom {
    padding-bottom: 60px;
  }
}

.section-space-small {
  padding: 80px 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-space-small {
    padding: 80px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-small {
    padding: 60px 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space-small {
    padding: 60px 0;
  }
}

.section-space-small-top {
  padding-top: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-space-small-top {
    padding-top: 60px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-space-small-top {
    padding-top: 60px;
  }
}

.section-title-spacing {
  margin-bottom: 55px;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .section-title-spacing {
    margin-bottom: 40px;
  }
}

.important-area {
  padding-top: 370px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .important-area {
    padding-top: 350px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .important-area {
    padding-top: 320px;
  }
}

/*----------------------------------------*/
/*  BREADCRUMB CSS START
/*----------------------------------------*/
.breadcrumb__thumb {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  mix-blend-mode: luminosity;
  opacity: 20%;
  z-index: -1;
}

.breadcrumb__title {
  font-size: 64px;
  margin-bottom: 15px;
  color: var(--clr-common-white);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb__title {
    font-size: 56px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .breadcrumb__title {
    font-size: 50px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .breadcrumb__title {
    font-size: 42px;
  }
}
@media (max-width: 450px) {
  .breadcrumb__title {
    font-size: 34px;
  }
}

.breadcrumb__menu ul {
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: -ms-inline-flexbox;
  padding-left: 22px;
  gap: 25px;
  justify-content: center;
}
.breadcrumb__menu ul li {
  list-style: none;
  position: relative;
  line-height: 1;
}
.breadcrumb__menu ul li:not(:first-child) span {
  color: var(--clr-common-white);
}
.breadcrumb__menu ul li:not(:last-child):after {
  display: inline-block;
  content: "";
  position: absolute;
  width: 5px;
  height: 5px;
  background: var(--clr-common-white);
  inset-inline-end: -15px;
  top: 50%;
  font-family: var(--bd-ff-fontawesome);
  font-size: 14px;
  transform: translateY(-50%);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.breadcrumb__menu ul li.active span {
  color: var(--clr-theme-primary);
}
.breadcrumb__menu ul li span {
  font-size: 16px;
  text-transform: capitalize;
  font-weight: var(--bd-fw-medium);
  color: var(--clr-common-white);
}
.breadcrumb__menu ul li span a {
  font-weight: var(--bd-fw-medium);
}
.breadcrumb__menu ul li span a:hover {
  color: var(--clr-theme-primary);
}

/*----------------------------------------*/
/*  CAROUSEL CSS START
/*----------------------------------------*/
.navigation__wrapprer {
  margin-top: 60px;
}

.banner-3-dots {
  position: absolute;
  top: 40%;
  right: 55px;
  z-index: 55;
  transform: translateY(-50%);
}
@media (max-width: 575px) {
  .banner-3-dots {
    position: inherit;
    transform: inherit;
  }
}

.banner-dot-3 {
  flex-direction: column;
  gap: 16px;
}
.banner-dot-3 .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: var(--clr-theme-secondary);
  opacity: 1;
  border-radius: 30px;
  position: relative;
}
.banner-dot-3 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--clr-theme-primary);
}

.bd-swiper-dot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 35px;
}
.bd-swiper-dot .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background: var(--clr-theme-secondary);
  opacity: 1;
  border-radius: 30px;
  position: relative;
  margin: 0 !important;
}
.bd-swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--clr-theme-primary);
}

/*----------------------------------------*/
/*  TAB CSS START
/*----------------------------------------*/
.about-tab .nav-tabs {
  padding: 0;
  margin: 0;
  border: 0;
}
.about-tab .nav-tabs .nav-link {
  padding: 0;
  margin: 0;
  border: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/*----------------------------------------*/
/*  SOCIAL CSS START
/*----------------------------------------*/
.theme-social {
  display: inline-flex;
  align-items: center;
  gap: 15px 15px;
}
.theme-social a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--clr-common-white);
  border-radius: 50%;
  color: rgb(22, 22, 22);
}
.theme-social a:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}

/*----------------------------------------*/
/*  BACKTOTOP CSS START
/*----------------------------------------*/
.backtotop-wrap {
  position: fixed;
  bottom: 35px;
  right: 30px;
  height: 44px;
  width: 44px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(20px);
  -ms-transform: translateY(20px);
  transform: translateY(20px);
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  color: var(--clr-common-heading);
  justify-content: center;
  background-color: var(--clr-common-white);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}
.backtotop-wrap:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 50px 0 rgba(0, 0, 0, 0.2);
}
.backtotop-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}
.backtotop-wrap::after {
  position: absolute;
  font-family: var(--bd-ff-fontawesome);
  content: "\f062";
  text-align: center;
  line-height: 44px;
  font-size: 16px;
  font-weight: 900;
  left: 0;
  top: 0;
  height: 44px;
  width: 44px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}
.backtotop-wrap svg path {
  fill: none;
}
.backtotop-wrap svg.backtotop-circle path {
  stroke: #ccc;
  stroke-width: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}

/*----------------------------------------*/
/* ANIMATION CSS STAR
/*----------------------------------------*/
@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    -webkit-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
@keyframes pulse {
  0% {
    -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    -moz-box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 45px rgba(255, 255, 255, 0);
  }
  100% {
    -moz-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
/*======================================
Animate slide-left 
========================================*/
@keyframes slide-left {
  from {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  to {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }
}
/*======================================
Animate card-bounce
========================================*/
@keyframes card-bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(-15px);
  }
}
@keyframes hero-circle-1 {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.quote-animation {
  animation-duration: 2.5s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
  animation-name: card-bounce;
}

/*======================================
Animate wobble-vertical
========================================*/
@-webkit-keyframes wobble-vertical {
  16.65% {
    transform: translateY(8px);
  }
  33.3% {
    transform: translateY(-6px);
  }
  49.95% {
    transform: translateY(4px);
  }
  66.6% {
    transform: translateY(-2px);
  }
  83.25% {
    transform: translateY(1px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes wobble-vertical {
  16.65% {
    transform: translateY(8px);
  }
  33.3% {
    transform: translateY(-6px);
  }
  49.95% {
    transform: translateY(4px);
  }
  66.6% {
    transform: translateY(-2px);
  }
  83.25% {
    transform: translateY(1px);
  }
  100% {
    transform: translateY(0);
  }
}
/*======================================
Animate wobble-vertical
========================================*/
@keyframes shape-rotate {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes rotateCircle {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotateCircle {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.rotate-circle {
  -webkit-animation-name: rotateCircle;
  animation-name: rotateCircle;
  -webkit-animation-duration: 20s;
  animation-duration: 20s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

/*----------------------------------------*/
/*  PRELOADER CSS START
/*----------------------------------------*/
#preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: #121212;
  z-index: 999;
}

.bd-loader-wrap {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  width: 100%;
}

.bd-loader-inner {
  position: fixed;
  width: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.bd-loader {
  margin-left: 92px;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .bd-loader {
    transform: scale(0.8);
    margin-left: 66px;
  }
}

.bd-loader-item {
  position: absolute;
  width: 6px;
  height: 80px;
  margin-top: -45px;
  border-radius: 0px;
  background-color: var(--clr-theme-primary);
  animation: bd-loader-aim 0.8s infinite;
  animation-direction: alternate-reverse;
}

.bd-loader .bd-loader-item:nth-child(1) {
  margin-left: 0px;
}
.bd-loader .bd-loader-item:nth-child(2) {
  margin-left: -14px;
  animation-delay: 0.1s;
}
.bd-loader .bd-loader-item:nth-child(3) {
  margin-left: -28px;
  animation-delay: 0.2s;
}
.bd-loader .bd-loader-item:nth-child(4) {
  margin-left: -42px;
  animation-delay: 0.3s;
}
.bd-loader .bd-loader-item:nth-child(5) {
  margin-left: -56px;
  animation-delay: 0.4s;
}
.bd-loader .bd-loader-item:nth-child(6) {
  margin-left: -70px;
  animation-delay: 0.5s;
}
.bd-loader .bd-loader-item:nth-child(7) {
  margin-left: -84px;
  animation-delay: 0.6s;
}
.bd-loader .bd-loader-item:nth-child(8) {
  margin-left: -98px;
  animation-delay: 0.7s;
}

@keyframes bd-loader-aim {
  0% {
    height: 2px;
    margin-top: 0;
    transform: rotate(0deg);
  }
  100% {
    height: 80px;
    transform: rotate(0deg);
  }
}
/*----------------------------------------*/
/*  MEAN MENU CSS START
/*----------------------------------------*/
/* Mean menu customize */
.mean-container a.meanmenu-reveal {
  display: none;
}

.mean-container .mean-nav {
  background: none;
  margin-top: 0;
}

.mean-container .mean-bar {
  padding: 0;
  min-height: auto;
  background: none;
}

.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}

.mean-container a.meanmenu-reveal {
  display: none !important;
}

.mean-container .mean-nav ul li a {
  width: 100%;
  padding: 10px 0;
  color: var(--clr-common-white);
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  text-transform: capitalize;
  border-top: 1px solid var(--clr-border-2);
}
.mean-container .mean-nav ul li a:last-child {
  border-bottom: 0;
}
.mean-container .mean-nav ul li a:hover {
  color: var(--clr-theme-primary);
}

.mean-container .mean-nav ul li a.mean-expand {
  margin-top: 5px;
  padding: 0 !important;
  height: 30px;
  width: 30px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--clr-common-white);
  line-height: 30px;
  top: 0;
  font-weight: 400;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

.mean-container .mean-nav ul li > a > i {
  display: none;
}

.mean-container .mean-nav ul li > a.mean-expand i {
  display: inline-block;
}

.mean-container .mean-nav > ul > li:first-child > a {
  border-top: 0;
}

.mean-container .mean-nav ul li a.mean-expand.mean-clicked i {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
}

.mean-container .mean-nav ul li .mega-menu li a {
  height: 200px;
  width: 100%;
  padding: 0;
  border-top: 0;
  margin-bottom: 20px;
}

/*----------------------------------------
PAGINATION CSS START
-----------------------------------------*/
.bd-basic__pagination ul {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .bd-basic__pagination ul {
    justify-content: start;
  }
}
.bd-basic__pagination ul li {
  list-style: none;
}
.bd-basic__pagination ul li a {
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  right: 0;
  top: 50%;
  font-weight: 600;
  font-size: 18px;
  border: 1px solid var(--clr-border-2);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.bd-basic__pagination ul li a:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}
.bd-basic__pagination ul li a:hover svg * {
  stroke: var(--clr-common-heading);
}
.bd-basic__pagination ul li .current {
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  right: 0;
  top: 50%;
  font-weight: 500;
  font-size: 16px;
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

/*----------------------------------------*/
/*  SEARCH CSS START
/*----------------------------------------*/
.df-search-area {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 9999;
  background-color: var(--clr-common-white);
  padding: 40px 15px;
  visibility: visible;
  opacity: 0;
  transform: translateY(-120%);
}
.df-search-area.opened {
  transform: translateY(0%);
  visibility: visible;
  opacity: 1;
}
.df-search-input {
  position: relative;
}
.df-search-input button {
  position: absolute;
  top: 54%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 24px;
}
.df-search-input button:hover {
  color: var(--tp-theme-primary);
}
.df-search-category span {
  color: var(--clr-common-black);
}
.df-search-category a {
  font-size: 14px;
  margin-left: 5px;
}
.df-search-category a:hover {
  color: var(--clr-theme-secondary);
}
.df-search-close {
  display: none;
}
.df-search-close-btn {
  border-radius: 50%;
  background: #ececec;
  border: 9px solid transparent;
  color: var(--clr-common-black);
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}
.df-search-close-btn::after,
.df-search-close-btn::before {
  content: "";
  position: absolute;
  height: 1px;
  width: 90%;
  top: 46%;
  left: 1px;
  transform-origin: 50% 50%;
  background-color: var(--clr-common-black);
  opacity: 1;
  -moz-transition: -moz-transform ease 0.25s;
  -webkit-transition: -webkit-transform ease 0.25s;
  -o-transition: -o-transform ease 0.25s;
  -ms-transition: -ms-transform ease 0.25s;
  transition: transform ease 0.25s;
}
.df-search-close-btn::before {
  transform: rotate(45deg);
}
.df-search-close-btn::after {
  transform: rotate(-45deg);
}
.df-search-close-btn:hover::before {
  transform: rotate(-45deg);
}
.df-search-close-btn:hover::after {
  transform: rotate(45deg);
}

/*----------------------------------------*/
/*  MENU CSS START
/*----------------------------------------*/
.main-menu ul {
  margin-bottom: 0;
}
.main-menu ul li {
  position: relative;
  list-style: none;
  display: inline-block;
  margin-inline-end: 25px;
  /* submenu css start */
  /* mega menu start */
}
.main-menu ul li:last-child {
  margin-inline-end: 0;
}
.main-menu ul li a {
  display: inline-block;
  font-size: 16px;
  font-weight: 500;
  color: var(--clr-common-heading);
  padding: 33px 0;
  text-align: left;
  position: relative;
}
.main-menu ul li.has-dropdown > a {
  position: relative;
}
.main-menu ul li.has-dropdown > a::after {
  content: "+";
  -webkit-transform: translateY(1px);
  -moz-transform: translateY(1px);
  -ms-transform: translateY(1px);
  -o-transform: translateY(1px);
  transform: translateY(1px);
  font-size: 14px;
  font-family: var(--bd-ff-fontawesome);
  font-weight: 600;
  margin-inline-start: 5px;
  display: inline-block;
}
.main-menu ul li.has-mega-menu {
  position: static;
}
.main-menu ul li .submenu {
  position: absolute;
  top: 100%;
  inset-inline-start: 0;
  min-width: 240px;
  background: var(--clr-common-white);
  z-index: 99;
  visibility: hidden;
  opacity: 0;
  transform-origin: top center;
  color: var(--clr-common-white);
  -webkit-box-shadow: 30px 30px 30px 30px rgba(11, 6, 70, 0.08);
  -moz-box-shadow: 30px 30px 30px 30px rgba(11, 6, 70, 0.08);
  box-shadow: 30px 30px 30px 30px rgba(11, 6, 70, 0.08);
}
.main-menu ul li .submenu li {
  display: block;
  width: 100%;
  margin: 0;
  padding: 0;
}
.main-menu ul li .submenu li.has-dropdown > a::after {
  position: absolute;
  top: 50%;
  inset-inline-end: 25px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  color: var(--clr-common-heading);
}
.main-menu ul li .submenu li a {
  position: relative;
  z-index: 11;
  color: var(--clr-common-heading);
  font-weight: 600;
  font-size: 14px;
  padding: 0 25px;
  padding-bottom: 11px;
  padding-top: 11px;
  width: 100%;
  border-bottom: 1px solid var(--clr-border-2);
}
.main-menu ul li .submenu li .submenu {
  inset-inline-start: 100%;
  top: 0;
  visibility: hidden;
  opacity: 0;
}
.main-menu ul li .submenu li:hover > a {
  background: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}
.main-menu ul li .submenu li:hover > a::after {
  color: var(--clr-common-white);
}
.main-menu ul li .submenu li:hover > .submenu {
  -webkit-transform: translateY(1);
  -moz-transform: translateY(1);
  -ms-transform: translateY(1);
  -o-transform: translateY(1);
  transform: translateY(1);
  visibility: visible;
  opacity: 1;
}
.main-menu ul li .mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  padding: 40px 225px;
  background: var(--clr-common-white);
  box-shadow: 0px 20px 30px rgba(3, 4, 28, 0.1);
  visibility: hidden;
  opacity: 0;
  transform-origin: top center;
  border: 1px solid #d9d9d9;
  border-bottom: 0;
  z-index: 99;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .main-menu ul li .mega-menu {
    padding: 40px 150px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .main-menu ul li .mega-menu {
    padding: 25px 25px;
    padding-inline-start: 80px;
  }
}
.main-menu ul li .mega-menu li {
  float: left;
  width: 25%;
  margin-inline-end: 0;
  text-align: left;
}
[dir="rtl"] .main-menu ul li .mega-menu li {
  float: right;
  margin-left: 0;
  text-align: right;
}
.main-menu ul li .mega-menu li .mega-menu-title {
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: var(--clr-common-heading);
  cursor: text;
  padding: 0;
  display: inline-block;
  margin-bottom: 15px;
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 5px;
}
.main-menu ul li .mega-menu li .mega-menu-title::before {
  display: none;
}
.main-menu ul li .mega-menu li ul li {
  width: 100%;
  float: none;
  margin-bottom: 5px;
}
.main-menu ul li .mega-menu li ul li:last-child {
  margin-bottom: 0;
}
.main-menu ul li .mega-menu li ul li a {
  font-weight: 500;
  font-size: 15px;
  color: #5d636a;
  padding: 0;
  position: relative;
}
.main-menu ul li .mega-menu li ul li a::before {
  position: absolute;
  content: "";
  left: 0;
  right: auto;
  top: 50%;
  bottom: auto;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 0;
  height: 1.2px;
  background-color: var(--clr-theme-secondary);
}
[dir="rtl"] .main-menu ul li .mega-menu li ul li a::before {
  right: 0;
  left: auto;
}
.main-menu ul li .mega-menu li ul li:hover > a {
  color: var(--clr-theme-secondary);
}
.main-menu ul li .mega-menu li ul li:hover > a::before {
  background-color: var(--clr-theme-secondary);
}
.main-menu ul li .mega-menu li:hover > .mega-menu-title {
  color: var(--clr-theme-secondary);
  padding-inline-start: 0;
}
.main-menu ul li .mega-menu li:hover > a {
  color: var(--clr-theme-secondary);
}
.main-menu ul li .mega-menu li a:hover {
  padding-inline-start: 18px;
}
[dir="rtl"] .main-menu ul li .mega-menu li a:hover {
  padding-inline-start: 18px;
  padding-inline-end: 0px;
}
.main-menu ul li .mega-menu li a:hover::before {
  width: 12px;
}
.main-menu ul li:hover > a {
  color: var(--clr-theme-secondary);
}
.main-menu ul li:hover > a::after {
  color: var(--clr-theme-secondary);
}
.main-menu ul li:hover.has-dropdown > a::after {
  content: "\f068";
}
.main-menu ul li:hover > .submenu {
  visibility: visible;
  opacity: 1;
}
.main-menu ul li:hover .mega-menu {
  visibility: visible;
  opacity: 1;
}
.main-menu-2 ul li a {
  color: var(--clr-common-white);
  position: relative;
}
.main-menu-2 ul li:hover > a {
  color: var(--clr-common-white);
}
.main-menu-2 ul li:hover > a::after {
  color: var(--clr-common-white);
}

/*----------------------------------------*/
/*  HEADER CSS START
/*----------------------------------------*/
.header-transparent {
  position: absolute;
  inset-inline-start: 0;
  margin: auto;
  width: 100%;
  z-index: 99;
}

.sticky {
  position: fixed !important;
  top: 0;
  z-index: 111;
  inset-inline-end: 0;
  inset-inline-start: 0;
  width: 100%;
  -webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
  background: var(--clr-common-white);
  -webkit-box-shadow: 0px 10px 20px 0px rgba(8, 0, 42, 0.08);
  -moz-box-shadow: 0px 10px 20px 0px rgba(8, 0, 42, 0.08);
  box-shadow: 0px 10px 20px 0px rgba(8, 0, 42, 0.08);
}

.header-top-area .header-lang-item {
  position: relative;
  padding: 12px 0;
}
.header-top-area .header-lang-item:not(:last-child) {
  padding-right: 15px;
  margin-right: 17px;
}
.header-top-area .header-lang-item > span {
  padding-left: 0;
  color: var(--clr-common-white);
  font-size: 14px;
}
.header-top-area .header-lang-item > span::after {
  color: var(--clr-common-white);
}
.header-top-area
  .header-lang-item:not(:last-child)
  .header-top-area
  .header-lang-item::after {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  width: 1px;
  height: 20px;
  background-color: rgba(255, 255, 255, 0.2);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.header-lang-item {
  position: relative;
  padding: 3px 14px 4px 18px;
}
.header-lang-item > span {
  position: relative;
  display: inline-block;
  font-weight: 500;
  font-size: 16px;
  color: var(--clr-common-heading);
  padding-right: 20px;
}
.header-lang-item > span:hover {
  cursor: pointer;
  color: var(--clr-theme-secondary);
}
.header-lang-item > span:hover::after {
  color: currentColor;
}
.header-lang-item > span::after {
  position: absolute;
  content: "\f107";
  font-family: var(--bd-ff-fontawesome);
  margin-left: 7px;
}
.header-lang-item ul {
  position: absolute;
  top: 125%;
  right: 0;
  z-index: 111;
  background-color: var(--clr-common-white);
  padding: 10px 15px;
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.12);
  visibility: hidden;
  opacity: 0;
  min-width: max-content;
  -webkit-transition: all 0.2s 0s ease-out;
  -moz-transition: all 0.2s 0s ease-out;
  -ms-transition: all 0.2s 0s ease-out;
  -o-transition: all 0.2s 0s ease-out;
  transition: all 0.2s 0s ease-out;
}
.header-lang-item ul.lang-list-open {
  top: 100%;
  visibility: visible;
  opacity: 1;
}
.header-lang-item ul li {
  list-style: none;
}
.header-lang-item ul li a {
  font-size: 14px;
  color: var(--clr-common-heading);
}
.header-lang-item ul li a:hover {
  color: var(--clr-theme-secondary);
}
.header-lang-item .tp-currency-list-open,
.header-lang-item .tp-setting-list-open {
  opacity: 1;
  visibility: visible;
  top: 100%;
}

.header-action-btn {
  font-size: 20px;
  color: var(--clr-common-black);
  position: relative;
  display: inline-block;
}

.header-action-badge {
  position: absolute;
  top: -6px;
  right: -13px;
  width: 23px;
  height: 23px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  font-size: 12px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.header-action-item:not(:first-child) {
  margin-left: 30px;
}

.bar-icon {
  width: 30px;
  height: 22px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}
.bar-icon span {
  width: 100%;
  height: 2px;
  background: var(--clr-common-heading);
  display: inline-block;
}
.bar-icon span:nth-child(2) {
  position: relative;
  width: 6.56px;
}
.bar-icon span:nth-child(2) small {
  position: absolute;
  height: 100%;
  background: var(--clr-common-heading);
  left: 12.56px;
  width: 18.06px;
  z-index: 555;
}
.bar-icon.is-white span {
  background: var(--clr-common-white);
}
.bar-icon.is-white span:nth-child(2) small {
  background: var(--clr-common-white);
}

.header-top-notice > p {
  margin-bottom: 0;
  color: var(--clr-common-white);
  font-weight: var(--bd-fw-medium);
  font-size: 14px;
}

.header-area {
  padding: 0 65px;
  overflow-x: clip;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .header-area {
    padding: 20px 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-area {
    padding: 0 15px;
  }
}
.header-area .fill-btn {
  height: 50px;
}

.header-logo {
  max-width: 170px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-logo {
    max-width: 145px;
  }
}
@media (max-width: 450px) {
  .header-logo {
    max-width: 130px;
  }
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-left {
    gap: 45px;
  }
}

.header-action {
  display: flex;
  align-items: center;
}

.header-2 {
  padding: 0 80px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px),
  only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-2 {
    padding: 0 30px;
  }
}

.header-quick-access {
  background: var(--clr-theme-secondary);
  border-top-left-radius: 56px;
  border-bottom-left-radius: 56px;
  position: relative;
  padding: 33.5px 30px;
  padding-inline-end: 0;
  gap: 60px;
}
.header-quick-access::before {
  position: absolute;
  content: "";
  width: 3000px;
  height: 100%;
  background: var(--clr-theme-secondary);
  top: 0;
  inset-inline-start: 100%;
}
.header-quick-access .divider-line {
  display: inline-block;
  position: absolute;
  width: 1px;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  z-index: 555;
  top: 0;
  right: 60px;
}

@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .header-2 {
    padding: 20px 0;
  }
}

.bar-icon-2 {
  width: 35px;
  height: 20px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}
.bar-icon-2 span {
  width: 100%;
  height: 2px;
  background: var(--clr-text-secondary);
  display: inline-block;
}
.bar-icon-2 span:nth-child(1) {
  margin-inline-start: -10px;
}
.bar-icon-2:hover span:nth-child(2) {
  margin-inline-start: 0;
}
.bar-icon-2.is-white span {
  background: var(--clr-common-white);
}

.header-main-3 {
  display: grid;
  grid-template-columns: 40% auto 40%;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .header-main-3 {
    grid-template-columns: 50% auto 35%;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .header-main-3 {
    grid-template-columns: 50% auto 38%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-main-3 {
    grid-template-columns: auto auto;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .header-main-3 {
    display: flex;
    width: 100%;
    flex-direction: row-reverse;
  }
}

.header-contact-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-contact-text span {
  font-size: 13px;
  color: var(--clr-text-body);
}
.header-contact-text h6 a:hover {
  color: var(--clr-theme-secondary);
}

.header-right-3 {
  display: flex;
  align-items: center;
  gap: 20px 20px;
}

.header-top-area {
  background: var(--clr-common-heading);
}

.header-4.sticky {
  padding: 0px 15px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .header-4.sticky {
    padding: 18px 15px;
  }
}

.header-to-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-to-main .link-text a {
  color: var(--clr-common-white);
  font-size: 14px;
}

.header-layout-4 {
  padding: 0 65px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .header-layout-4 {
    padding: 0 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .header-layout-4 {
    padding: 0 15px;
  }
}

@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .header-layout-4.header-bottom {
    padding-top: 18px;
    padding-bottom: 18px;
  }
}

.header-main-4 {
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .header-main-4 {
    grid-template-columns: auto auto;
  }
}

.header-search {
  width: 100%;
  position: relative;
  max-width: 302px;
}
.header-search input {
  height: 50px;
  font-size: 14px;
  border-radius: 30px;
  padding-right: 60px;
}
.header-search button {
  position: absolute;
  top: 50%;
  right: 0px;
  transform: translateY(-50%);
  background: var(--clr-theme-primary);
  height: 49px;
  width: 49px;
  border-radius: 50%;
}

.header-area.sticky .main-menu-2 ul li a {
  color: var(--clr-common-heading);
}
.header-area.sticky .main-menu-2 ul li:hover > a::after {
  color: var(--clr-common-heading);
}
.header-area.sticky .bar-icon-2.is-white span {
  background: var(--clr-common-heading);
}

.header-5 {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-black {
  display: none;
}

.header-area.sticky .logo-black {
  display: block;
}
.header-area.sticky .logo-white {
  display: none;
}

.header-main-5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/*----------------------------------------*/
/*  BANNER CSS START
/*----------------------------------------*/
.banner-area .container {
  max-width: 1590px;
}

.banner-1 {
  padding-top: 100px;
}
.banner-1 .banner-overlay-1 {
  height: 100%;
  width: 50%;
  background: rgba(235, 117, 59, 0.2);
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
}
@media (max-width: 575px) {
  .banner-1 .banner-overlay-1 {
    width: 0%;
  }
}

.round-box-1 {
  position: relative;
  top: 0;
  left: 70px;
  opacity: 30%;
  z-index: -1;
}
.round-box-1 .banner-round-1 {
  position: absolute;
  width: 354.03px;
  height: 354.03px;
  background-color: rgba(3, 175, 229, 0.2);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
.round-box-1 .banner-round-2 {
  position: absolute;
  width: 511.03px;
  height: 511.03px;
  background-color: rgba(3, 175, 229, 0.15);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
.round-box-1 .banner-round-3 {
  position: absolute;
  width: 653.65px;
  height: 653.65px;
  background-color: rgba(3, 175, 229, 0.1);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}

.round-box-2 {
  position: absolute;
  right: 50px;
  bottom: 50px;
  z-index: -1;
}
.round-box-2 .banner-round-1 {
  position: absolute;
  width: 202.21px;
  height: 202.21px;
  background-color: rgba(235, 117, 59, 0.2);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
.round-box-2 .banner-round-2 {
  position: absolute;
  width: 291.89px;
  height: 291.89px;
  background-color: rgba(235, 117, 59, 0.15);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
.round-box-2 .banner-round-3 {
  position: absolute;
  width: 373.35px;
  height: 373.35px;
  background-color: rgba(235, 117, 59, 0.1);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}

.banner-grid {
  display: grid;
  grid-template-columns: auto 630px auto;
  row-gap: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-grid {
    grid-template-columns: auto 420px auto;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-grid {
    grid-template-columns: auto 300px auto;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-grid {
    grid-template-columns: auto;
    column-gap: 50px;
  }
}

@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-thumb-wrapper {
    grid-row: 12/3;
  }
}

.banner-thumb {
  max-width: 630px;
  margin: 0 auto;
}

.banner-content {
  margin-top: 135px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-content {
    margin-top: 55px;
    margin-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-content {
    margin-top: 65px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-content {
    margin-top: 50px;
  }
}
@media (max-width: 575px) {
  .banner-content {
    margin-top: 35px;
  }
}
.banner-content > span {
  background-color: rgba(3, 175, 229, 0.2);
  padding: 11px 20px 11px 34px;
  line-height: 1;
  display: inline-block;
  border-radius: 40px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 30px;
  position: relative;
}
.banner-content > span:after {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 19px;
  background-color: var(--clr-common-white);
  border: 5px slid var(--clr-theme-secondary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.banner-content > span::before {
  position: absolute;
  content: "";
  height: 11px;
  width: 11px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 16px;
  background-color: var(--clr-theme-primary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.banner-content p {
  margin-bottom: 45px;
  max-width: 413px;
}

.banner-title {
  font-size: 86px;
  line-height: 1;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-title {
    font-size: 60px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-title {
    font-size: 52px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-title {
    font-size: 66px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-title {
    font-size: 55px;
  }
}
@media (max-width: 575px) {
  .banner-title {
    font-size: 40px;
    line-height: 1.2;
  }
}
@media (max-width: 450px) {
  .banner-title {
    font-size: 36px;
  }
}

.banner-shape-inner {
  background: #eb753b;
  opacity: 0.2;
  position: absolute;
  height: 100%;
  width: 50%;
  top: 0;
  right: 0;
  z-index: -1;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-shape-inner {
    display: none;
  }
}

.banner-shape-2 {
  position: absolute;
  left: 39%;
  top: 41%;
  transform: translate(-50%, -50%);
  -webkit-animation: shape-rotate 6s linear 0s infinite alternate;
  -moz-animation: shape-rotate 6s linear 0s infinite alternate;
  -o-animation: shape-rotate 6s linear 0s infinite alternate;
  animation: shape-rotate 6s linear 0s infinite alternate;
}

.banner-description {
  margin-top: 75px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-description {
    margin-top: 0;
  }
}
.banner-description .description-user span {
  font-weight: 600;
  font-size: 20px;
  color: var(--clr-theme-primary);
}

.health-icon {
  margin-bottom: 20px;
}
.health-icon span {
  width: 40px;
  height: 60px;
  background: var(--clr-theme-secondary);
  border-radius: 41.81px;
  display: inline-block;
  text-align: center;
  line-height: 60px;
}

.banner-card {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: var(--clr-common-white);
  padding: 15px 25px 15px 15px;
  position: absolute;
  top: 55%;
  right: -1%;
  transform: translate(-50%, -50%);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-card {
    top: 65%;
  }
}
.banner-card span {
  height: 84px;
  width: 84px;
  background-color: rgba(3, 175, 229, 0.2);
  text-align: center;
  line-height: 74px;
  display: inline-block;
}
.banner-card .banner-card-text h3 {
  font-size: 36px;
  color: var(--clr-theme-primary);
  font-weight: 600;
  line-height: normal;
  letter-spacing: -1.834px;
}
.banner-card .banner-card-text P {
  margin-bottom: 0;
}

.banner-inner-grid {
  display: grid;
  grid-template-columns: 50% 50%;
  align-items: center;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-inner-grid {
    grid-template-columns: auto;
  }
}

.banner-wrapper-2 {
  height: 100%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-wrapper-2 {
    min-height: 450px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-wrapper-2 {
    min-height: 400px;
  }
}

.banner-thumb-2 {
  position: absolute;
  height: 100%;
  width: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 0 0 0 40px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-thumb-2 {
    border-radius: 0;
  }
}

.banner-content-2 {
  padding: 185px 120px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .banner-content-2 {
    padding: 115px 80px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-content-2 {
    padding: 100px 60px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-content-2 {
    padding: 180px 50px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-content-2 {
    padding: 100px 66px;
  }
}
@media (max-width: 575px) {
  .banner-content-2 {
    padding: 80px 15px;
  }
}
.banner-content-2 h2 {
  font-size: 100px;
  line-height: 1.1;
  color: var(--clr-common-white);
  margin-bottom: 30px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .banner-content-2 h2 {
    font-size: 86px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-content-2 h2 {
    font-size: 80px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-content-2 h2 {
    font-size: 66px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-content-2 h2 {
    font-size: 60px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-content-2 h2 {
    font-size: 56px;
  }
}
@media (max-width: 575px) {
  .banner-content-2 h2 {
    font-size: 45px;
  }
}
.banner-content-2 > P {
  margin-bottom: 45px;
  color: var(--clr-common-white);
  max-width: 480px;
}
.banner-content-2 .move-shape {
  position: absolute;
  bottom: 6%;
  left: 38%;
  transform: rotate(-21.461deg);
}

.banner-subtitle-2 {
  text-transform: capitalize;
  color: var(--clr-common-white);
  margin-bottom: 20px;
  display: block;
  position: relative;
  padding-left: 45px;
}
.banner-subtitle-2:after {
  position: absolute;
  content: "";
  width: 32px;
  height: 12px;
  top: 50%;
  left: 0;
  background: linear-gradient(-45deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  transform: translateY(-50%);
  border-radius: 30px;
}

.banner-bg-thumb {
  background: var(--clr-theme-primary);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.banner-bg {
  width: 100%;
  height: 100%;
  mix-blend-mode: overlay;
  opacity: 50%;
}

.banner-card-meta {
  background: var(--clr-common-white);
  display: inline-block;
  padding: 20px 20px;
  position: absolute;
  top: 50%;
  left: 56%;
  transform: translate(-50%, -50%);
  box-shadow: 0px 40px 40px rgba(0, 0, 0, 0.1);
  min-width: 260px;
}
.banner-card-meta .thumb {
  max-width: 55px;
  display: inline-block;
  border: 3px solid var(--clr-common-white);
  border-radius: 50%;
  margin-right: -18px;
  margin-bottom: 5px;
}
.banner-card-meta .thumb img {
  width: 100%;
}

.banner-card-meta h2 {
  font-size: 48px;
  color: var(--clr-theme-primary);
  font-weight: 600;
}
@media (max-width: 575px) {
  .banner-card-meta h2 {
    font-size: 35px;
  }
}
.banner-card-meta p {
  margin-bottom: 0;
}

.banner-3 {
  background: #e6f7fc;
  padding: 200px 160px 200px;
  max-width: 1920px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .banner-3 {
    padding: 120px 80px 120px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-3 {
    padding: 120px 80px 120px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-3 {
    padding: 120px 80px 120px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-3 {
    padding: 100px 60px 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-3 {
    padding: 80px 30px 450px;
  }
}
@media (max-width: 575px) {
  .banner-3 {
    padding: 80px 30px 450px;
  }
}
.banner-3 .banner-round {
  width: 863px;
  height: 863px;
  border-radius: 863px;
  background: linear-gradient(
    225deg,
    rgba(3, 175, 229, 0.07) 0%,
    rgba(3, 175, 229, 0) 100%
  );
  position: absolute;
  left: -200px;
  z-index: -1;
  top: 84px;
}
.banner-3 .banner-round-2 {
  border-radius: 50%;
  width: 255px;
  height: 255px;
  background: linear-gradient(
    224.59deg,
    rgba(3, 175, 229, 0.1) 0%,
    rgba(3, 175, 229, 0) 100%
  );
  position: absolute;
  right: -55px;
  bottom: calc(100% - 150px);
}
@media (max-width: 575px) {
  .banner-3 .banner-round-2 {
    display: none;
  }
}

.banner-thumb-3 {
  position: absolute;
  height: 100%;
  width: 80%;
  top: 0;
  right: 0;
  z-index: -1;
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-thumb-3 {
    background-position: 0%;
    width: 90%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-thumb-3 {
    height: 450px;
    width: 100%;
    top: auto;
    bottom: 0;
  }
}

.banner-content-3 {
  max-width: 556px;
}
.banner-content-3 > span {
  font-size: 14px;
  text-transform: uppercase;
  margin-bottom: 15px;
  display: block;
  color: var(--clr-theme-primary);
}
.banner-content-3 h2 {
  font-size: 74px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 35px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .banner-content-3 h2 {
    font-size: 66px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-content-3 h2 {
    font-size: 50px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-content-3 h2 {
    font-size: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-content-3 h2 {
    font-size: 50px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-content-3 h2 {
    font-size: 44px;
  }
}
@media (max-width: 575px) {
  .banner-content-3 h2 {
    font-size: 36px;
  }
}
@media (max-width: 450px) {
  .banner-content-3 h2 {
    font-size: 32px;
  }
}
.banner-content-3 > p {
  margin-bottom: 28px;
}

.banner-btn-group {
  display: flex;
  align-items: center;
  gap: 20px 30px;
  flex-wrap: wrap;
}

.banner-video {
  display: flex;
  align-items: center;
  gap: 15px;
}
.banner-video h6 {
  max-width: 141px;
}

.banner-play {
  height: 60px;
  width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-theme-primary);
  border-radius: 50%;
  color: var(--clr-common-white);
  font-size: 18px;
}
.banner-play:hover {
  color: var(--clr-common-white);
}
.banner-play:focus {
  color: var(--clr-common-white);
}

.banner-social {
  position: absolute;
  top: 40%;
  z-index: 55;
  transform: translateY(-50%);
  right: 60px;
}
.banner-social ul li {
  list-style: none;
  margin-bottom: 10px;
}
.banner-social ul li a {
  border: 1px solid var(--clr-border-2);
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #585b5c;
}
.banner-social ul li a:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}

.banner-4 {
  background: linear-gradient(
    90.19deg,
    rgb(230, 247, 252) 0%,
    rgb(245, 254, 255) 100%
  );
}

.banner-item-4 {
  min-height: 700px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-item-4 {
    min-height: auto;
    padding-top: 80px;
  }
}
@media (max-width: 450px) {
  .banner-item-4 {
    padding-top: 60px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-thumb-4 {
    max-width: 500px;
    margin: auto;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-thumb-4 {
    max-width: 350px;
    margin: auto;
  }
}
.banner-thumb-4 img {
  width: 100%;
}

.banner-dot-inner {
  position: absolute;
  top: 50%;
  right: 60px;
  transform: translateY(-50%);
  z-index: 55;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-dot-inner {
    top: auto;
    right: auto;
    transform: translateX(-50%);
    bottom: 30px;
    left: 50%;
  }
}

.banner-content-4 p {
  max-width: 400px;
}

.banner-dot {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-dot {
    display: block;
  }
}
.banner-dot .swiper-pagination-bullet {
  background-color: #161616;
}
.banner-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #eb753b;
}

.banner-discount {
  position: absolute;
  right: 130px;
  top: 0;
  transform: rotate(-10.034deg);
}

.banner-content-4 > span {
  font-weight: 600;
  text-transform: uppercase;
  background: rgba(3, 175, 229, 0.15);
  padding: 4px 15px;
  font-size: 14px;
  display: inline-block;
  margin-bottom: 20px;
}
.banner-content-4 > p {
  margin-bottom: 30px;
}

.banner-title-4 {
  font-size: 80px;
  font-style: normal;
  font-weight: 600;
  line-height: 1.1;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .banner-title-4 {
    font-size: 76px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .banner-title-4 {
    font-size: 65px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .banner-title-4 {
    font-size: 44px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .banner-title-4 {
    font-size: 36px;
  }
}
@media (max-width: 575px) {
  .banner-title-4 {
    font-size: 36px;
  }
}
@media (max-width: 450px) {
  .banner-title-4 {
    font-size: 32px;
  }
}

.banner-btn-wrapper {
  display: flex;
  align-items: center;
  gap: 25px 20px;
  flex-wrap: wrap;
}

.banner-4-shape .shape-1 {
  position: absolute;
  left: 20%;
  top: 20%;
}
.banner-4-shape .shape-2 {
  position: absolute;
  left: 80px;
  bottom: 0;
}
.banner-4-shape .shape-3 {
  position: absolute;
  right: 0;
  top: 20%;
}

.slider-nav-prev {
  max-width: 352px;
  padding: 30px 30px;
  background: rgba(3, 175, 229, 0.6);
  backdrop-filter: blur(15px);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-prev {
    max-width: 270px;
  }
}
.slider-nav-prev p {
  color: var(--clr-common-white);
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-prev p {
    font-size: 20px;
  }
}

.slider-button-prev {
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: var(--clr-common-white);
  border: 1px solid var(--clr-common-white);
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: var(--clr-common-white);
  border: 1px solid var(--clr-common-white);
}

.slider-nav-next {
  max-width: 352px;
  padding: 30px 30px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-next {
    max-width: 270px;
  }
}
.slider-nav-next p {
  color: rgb(22, 22, 22);
  font-size: 24px;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-next p {
    font-size: 20px;
  }
}

.slider-button-next {
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: rgb(22, 22, 22);
  border: 1px solid rgb(22, 22, 22);
}

.slider__navigation {
  display: flex;
  position: absolute;
  bottom: 30px;
  z-index: 554;
  gap: 20px;
  right: 10%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation {
    right: 20px;
  }
}

/*----------------------------------------*/
/*  SLIDER CSS START
/*----------------------------------------*/
.slider-height {
  background-color: rgb(237, 249, 253);
  padding-top: 140px;
  padding-bottom: 200px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-height {
    padding-top: 120px;
    padding-bottom: 120px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-height {
    padding-top: 80px;
    padding-bottom: 80px;
    height: 680px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-height {
    padding-top: 100px;
    padding-bottom: 120px;
  }
}

.slider-content {
  padding-left: 220px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-content {
    padding-left: 80px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .slider-content {
    padding: 0px 50px 0px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-content {
    padding: 0px 30px 0px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-content {
    padding: 0px 30px 0px;
  }
}
@media (max-width: 450px) {
  .slider-content {
    padding: 0px 15px 0px;
  }
}

.slider-subtitle {
  font-size: 20px;
  margin-bottom: 35px;
  display: inline-block;
  padding-left: 10px;
  position: relative;
}
.slider-subtitle:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 2px;
  background: var(--clr-theme-primary);
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.slider-title {
  font-size: 80px;
  margin-bottom: 20px;
  font-weight: 600;
  line-height: 1.1;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-title {
    font-size: 74px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .slider-title {
    font-size: 66px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-title {
    font-size: 56px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-title {
    font-size: 50px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-title {
    font-size: 44px;
  }
}
@media (max-width: 575px) {
  .slider-title {
    font-size: 40px;
  }
}
@media (max-width: 450px) {
  .slider-title {
    font-size: 30px;
  }
}

.slide-video .slider-link {
  height: 60px;
  width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--clr-border-2);
  border-radius: 50%;
  font-size: 18px;
}
.slide-video .slider-link:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}
.slide-video span {
  margin-left: 10px;
}

.slider-btn-wrapper {
  display: flex;
  align-items: center;
  gap: 15px 20px;
  flex-wrap: wrap;
}

.slider-thumb-wrapper {
  height: 841px;
  width: 100%;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-thumb-wrapper {
    height: 650px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-thumb-wrapper {
    font-size: 700px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-thumb-wrapper {
    height: 450px;
  }
}
@media (max-width: 575px) {
  .slider-thumb-wrapper {
    height: 350px;
  }
}

.slider-nav-prev {
  max-width: 352px;
  padding: 30px 30px;
  background: rgba(3, 175, 229, 0.6);
  backdrop-filter: blur(15px);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-prev {
    max-width: 270px;
  }
}
.slider-nav-prev p {
  color: var(--clr-common-white);
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-prev p {
    font-size: 20px;
  }
}

.slider-button-prev {
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: var(--clr-common-white);
  border: 1px solid var(--clr-common-white);
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: var(--clr-common-white);
  border: 1px solid var(--clr-common-white);
}

.slider-nav-next {
  max-width: 352px;
  padding: 30px 30px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-next {
    max-width: 270px;
  }
}
.slider-nav-next p {
  color: rgb(22, 22, 22);
  font-size: 24px;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-nav-next p {
    font-size: 20px;
  }
}

.slider-button-next {
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: rgb(22, 22, 22);
  border: 1px solid rgb(22, 22, 22);
}

.slider__navigation {
  position: absolute;
  bottom: 30px;
  z-index: 554;
  gap: 20px;
  right: 10%;
}
.slider__navigation button:hover {
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
  border-color: transparent;
}
.slider__navigation .slider-nav-prev {
  max-width: 352px;
  padding: 30px 30px;
  background: rgba(3, 175, 229, 0.6);
  backdrop-filter: blur(15px);
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation .slider-nav-prev {
    max-width: 270px;
  }
}
.slider__navigation .slider-nav-prev p {
  color: var(--clr-common-white);
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation .slider-nav-prev p {
    font-size: 20px;
  }
}
.slider__navigation .slider-button-prev {
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: var(--clr-common-white);
  border: 1px solid var(--clr-common-white);
}
.slider__navigation .slider-nav-next {
  max-width: 352px;
  padding: 30px 30px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation .slider-nav-next {
    max-width: 270px;
  }
}
.slider__navigation .slider-nav-next p {
  color: rgb(22, 22, 22);
  font-size: 24px;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 1.5;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation .slider-nav-next p {
    font-size: 20px;
  }
}
.slider__navigation .slider-button-next {
  height: 55px;
  width: 55px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  color: rgb(22, 22, 22);
  border: 1px solid rgb(22, 22, 22);
}
.slider__navigation .slider__navigation {
  display: flex;
  position: absolute;
  bottom: 30px;
  z-index: 554;
  gap: 20px;
  right: 10%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation .slider__navigation {
    right: 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider__navigation {
    right: 20px;
  }
}

.slider-pagination {
  position: absolute;
  left: 220px;
  bottom: 50px;
  z-index: 55;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-pagination {
    left: 80px;
  }
}

@media (min-width: 1601px) {
  .slider-area-2 .container {
    max-width: 1500px;
  }
}
.slider-area-2 {
  background: var(--clr-theme-primary);
  display: flex;
  align-items: end;
  padding-top: 100px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-area-2 {
    padding-top: 160px;
  }
}
@media (max-width: 450px) {
  .slider-area-2 {
    padding-top: 145px;
  }
}

.banner-social-two {
  position: absolute;
  border-radius: 60px;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  z-index: 12;
}
.banner-social-two ul {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.banner-social-two ul li {
  list-style: none;
}
.banner-social-two ul li a {
  height: 44px;
  width: 44px;
  border: 1px solid rgb(255, 255, 255);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--clr-common-white);
}
.banner-social-two ul li a:hover {
  background-color: var(--clr-common-white);
  color: var(--clr-theme-primary);
  border-color: transparent;
}

.slider-subtitle-2 {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  padding: 4px 10px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  display: inline-block;
  margin-bottom: 20px;
}

.slider-title-2 {
  color: var(--clr-common-white);
  font-size: 80px;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-title-2 {
    font-size: 66px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .slider-title-2 {
    font-size: 60px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-title-2 {
    font-size: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-title-2 {
    font-size: 46px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-title-2 {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .slider-title-2 {
    font-size: 34px;
  }
}
@media (max-width: 450px) {
  .slider-title-2 {
    font-size: 28px;
  }
}

.slider-content-2 > p {
  color: var(--clr-common-white);
  margin-bottom: 35px;
  max-width: 450px;
}

.slider-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.slider-meta-content > p {
  margin-bottom: 0;
  color: var(--clr-common-white);
}

.slider-meta-rating span {
  color: #ffbe11;
  font-size: 14px;
}

.slider-meta-thumb img {
  border: 3px solid var(--clr-common-white);
  border-radius: 50%;
}
@media (max-width: 575px) {
  .slider-meta-thumb img {
    max-width: 40px;
  }
}
.slider-meta-thumb img:not(:last-child) {
  margin-right: -28px;
}

.slider-round {
  background: #caeaff;
  border-radius: 50%;
  width: 782px;
  height: 782px;
  position: absolute;
  bottom: 0;
  z-index: -1;
  left: 50%;
  transform: translateX(-50%);
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .slider-round {
    width: 680px;
    height: 680px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .slider-round {
    width: 600px;
    height: 600px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-round {
    width: 500px;
    height: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-round {
    width: 500px;
    height: 500px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-round {
    width: 500px;
    height: 500px;
  }
}
@media (max-width: 575px) {
  .slider-round {
    width: 420px;
    height: 420px;
  }
}
@media (max-width: 450px) {
  .slider-round {
    width: 320px;
    height: 320px;
  }
}

.slider-thumb-wrapper-2 .slider-thumb-2 {
  max-width: 621px;
  margin: 0 0 auto;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-thumb-wrapper-2 .slider-thumb-2 {
    max-width: 450px;
    margin: auto;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-thumb-wrapper-2 .slider-thumb-2 {
    max-width: 400px;
    margin: auto;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-thumb-wrapper-2 .slider-thumb-2 {
    max-width: 380px;
    margin: auto;
  }
}
.slider-thumb-wrapper-2 .slider-thumb-2 img {
  width: 100%;
}
.slider-thumb-wrapper-2 .glow {
  background: #a8eaff;
  border-radius: 50%;
  width: 717px;
  height: 717px;
  filter: blur(200px);
  position: absolute;
  z-index: -12;
  top: 0;
  right: 0%;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .slider-thumb-wrapper-2 .glow {
    width: 600px;
    height: 600px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .slider-thumb-wrapper-2 .glow {
    width: 500px;
    height: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-thumb-wrapper-2 .glow {
    width: 500px;
    height: 500px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .slider-thumb-wrapper-2 .glow {
    width: 500px;
    height: 500px;
  }
}
@media (max-width: 575px) {
  .slider-thumb-wrapper-2 .glow {
    width: 500px;
    height: 500px;
  }
}
@media (max-width: 450px) {
  .slider-thumb-wrapper-2 .glow {
    width: 500px;
    height: 500px;
  }
}

.slider-link-play {
  height: 60px;
  width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  font-size: 18px;
  color: var(--clr-common-body);
}
.slider-link-play:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}

/*----------------------------------------*/
/*  INTRO CSS START
/*----------------------------------------*/
.intro-bg-thumb {
  width: 100%;
  height: 100%;
  mix-blend-mode: soft-light;
  opacity: 60%;
}

.intro-bg {
  background: var(--clr-theme-primary);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/*----------------------------------------*/
/*  FEATURES CSS START
/*----------------------------------------*/
.features-item {
  display: flex;
  align-items: center;
  gap: 20px;
}
.features-item:hover .features-icon span img {
  animation-name: wobble-vertical;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

.features-icon span {
  width: 90px;
  line-height: 90px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 100%
  );
  border-radius: 68px;
  display: inline-block;
  text-align: center;
  padding: 30px 0;
}
@media (max-width: 450px) {
  .features-icon span {
    width: 80px;
    line-height: 60px;
    padding: 20px 0;
  }
  .features-icon span img {
    max-width: 36px;
  }
}

.features-content h4 {
  margin-bottom: 15px;
  color: var(--clr-common-white);
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .features-content h4 {
    font-size: 20px;
  }
}
.features-content p {
  margin-bottom: 0;
  color: var(--clr-common-white);
}

/*----------------------------------------*/
/*  ABOUT CSS START
/*----------------------------------------*/
.round-box-2 {
  position: absolute;
  right: 50px;
  bottom: 50px;
  z-index: -1;
}
.round-box-2 .banner-round-1 {
  position: absolute;
  width: 202.21px;
  height: 202.21px;
  background-color: rgba(235, 117, 59, 0.2);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .round-box-2 .banner-round-1 {
    width: 100.21px;
    height: 100.21px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .round-box-2 .banner-round-1 {
    display: none;
  }
}
.round-box-2 .banner-round-2 {
  position: absolute;
  width: 291.89px;
  height: 291.89px;
  background-color: rgba(235, 117, 59, 0.15);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .round-box-2 .banner-round-2 {
    width: 121.89px;
    height: 121.89px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .round-box-2 .banner-round-2 {
    display: none;
  }
}
.round-box-2 .banner-round-3 {
  position: absolute;
  width: 373.35px;
  height: 373.35px;
  background-color: rgba(235, 117, 59, 0.1);
  border-radius: 1000px;
  top: 0;
  left: 0;
  transform: translate(-50%, -50%);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .round-box-2 .banner-round-3 {
    width: 153.35px;
    height: 153.35px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .round-box-2 .banner-round-3 {
    display: none;
  }
}

.about-thumb-wrapper {
  max-width: 662px;
  padding-bottom: 65px;
  margin-top: 30px;
}

.about-dot {
  position: absolute;
  top: -30px;
  left: 0;
  z-index: -1;
}

.about-thumb {
  max-width: 510px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .about-thumb {
    max-width: 100%;
  }
}

.about-thumb-2 {
  max-width: 250px;
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  background: var(--clr-common-white);
}
.about-thumb-2 .play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.about-thumb-2 .play-btn span {
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-common-white);
  border-radius: 50%;
  -webkit-animation: pulse 2s infinite;
  -moz-animation: pulse 2s infinite;
  -o-animation: pulse 2s infinite;
  animation: pulse 2s infinite;
}
.about-thumb-2 .play-btn span:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}

.about-rectangle {
  position: absolute;
  width: 360px;
  height: 496px;
  opacity: 0.4;
  border: 10px solid #eb753b;
  border-radius: 0px;
  bottom: 45px;
  z-index: -1;
  right: 16%;
}

.about-content-box {
  padding-left: 60px;
  padding-right: 50px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-content-box {
    padding-left: 0;
    padding-right: 0;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-content-box {
    padding-left: 30px;
    padding-right: 0;
  }
}
.about-content-box > p {
  margin-bottom: 40px;
}

.about-info-icon {
  margin-bottom: 20px;
}
.about-info-icon span {
  height: 65px;
  width: 65px;
  background: var(--clr-theme-primary);
  display: inline-block;
  text-align: center;
  line-height: 65px;
  border-radius: 50%;
}

.about-info-content h4 {
  margin-bottom: 10px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-info-content h4 {
    font-size: 20px;
  }
}
.about-info-content p {
  margin-bottom: 0;
}

.about-thumb-wrapper-2 {
  padding-left: 64px;
  padding-right: 20px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-thumb-wrapper-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

.about-experience {
  background: var(--clr-common-white);
  max-width: 215px;
  position: absolute;
  bottom: 30px;
  right: 0;
  padding: 25px 25px;
}
.about-experience h2 {
  color: var(--clr-theme-primary);
  font-weight: 600;
}

.about-content-box-2 {
  padding-left: 60px;
  padding-right: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-content-box-2 {
    padding-left: 0;
    padding-right: 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-content-box-2 {
    padding-left: 0;
    padding-right: 0;
  }
}

.about-cercle {
  position: absolute;
  left: 0;
  top: 120px;
  border: 10px solid var(--clr-common-white);
  border-radius: 50%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-cercle {
    left: -25px;
    top: 50px;
  }
}
@media (max-width: 450px) {
  .about-cercle {
    display: none;
  }
}

.about__shape {
  position: absolute;
  right: -130px;
  bottom: 0;
  mix-blend-mode: luminosity;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .about__shape {
    max-width: 280px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .about__shape {
    display: none;
  }
}

.about-thumb-grid {
  margin: 65px 0;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-thumb-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .about-thumb-grid {
    grid-template-columns: 1fr;
  }
}

.about-thumb-3 img {
  width: 100%;
}
.about-thumb-3:hover img {
  transform: scale(1.05);
}
.about-thumb-3:nth-child(1) {
  height: calc(100% - 40px);
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .about-thumb-3:nth-child(1) {
    height: 100%;
  }
}
.about-thumb-3:nth-child(3) {
  height: calc(100% - 80px);
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .about-thumb-3:nth-child(3) {
    height: 100%;
  }
}

.about-thumb-wrapper-4 {
  position: relative;
  padding-bottom: 164px;
}
.about-thumb-wrapper-4 .about-thumb {
  max-width: 565px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .about-thumb-wrapper-4 .about-thumb {
    width: 100%;
  }
}
.about-thumb-wrapper-4 .about-thumb img {
  width: 100%;
}
.about-thumb-wrapper-4 .about-thumb--2 {
  position: absolute;
  right: 0;
  bottom: 0;
  border: 7px solid var(--clr-common-white);
  border-bottom: 0;
  z-index: 5;
}

.about-thumb-logo {
  padding: 60px 90px;
  background: var(--clr-theme-secondary);
  position: absolute;
  bottom: 0;
  left: 0;
}
@media (max-width: 450px) {
  .about-thumb-logo {
    padding: 20px 50px;
  }
}

.about-content-4 {
  padding-left: 80px;
  padding-right: 40px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-content-4 {
    padding: 0;
  }
}

.about-tab-content > p {
  margin-bottom: 35px;
}

.about-round {
  background: linear-gradient(
    180deg,
    rgb(3, 175, 229) 0%,
    rgba(3, 175, 229, 0) 100%
  );
  border-radius: 50%;
  opacity: 0.2;
  width: 190px;
  height: 190px;
  position: absolute;
  top: -60px;
  left: -105px;
  z-index: -1;
}

.about-tab-info {
  display: flex;
}

.about-tab-info-item {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media (max-width: 575px) {
  .about-tab-info-item {
    flex-wrap: wrap;
  }
}
.about-tab-info-item .info-item {
  border: 1px solid var(--clr-border-2);
  padding: 15px 19px;
  display: flex;
  align-items: center;
  gap: 15px;
  max-width: 260px;
}
.about-tab-info-item .info-item h2 {
  font-size: 40px;
  font-weight: 600;
  color: var(--clr-theme-primary);
}
.about-tab-info-item .info-item > p {
  margin-bottom: 0;
  line-height: 20px;
}

.about-meta-wrapper {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
}

.postbox-thumb img {
  max-width: 65px;
}

.postbox-meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.postbox-content h4 {
  font-size: 20px;
  margin-bottom: 2px;
}
.postbox-content span {
  color: var(--clr-text-body);
}

.about-tab .nav-tabs {
  background: rgba(3, 175, 229, 0.1);
  margin-bottom: 40px;
  border-bottom: 1px solid var(--clr-theme-primary);
  justify-content: space-between;
}
@media (max-width: 575px) {
  .about-tab .nav-tabs {
    justify-content: center;
    flex-direction: column;
  }
}
.about-tab .nav-tabs .nav-link {
  height: 50px;
  width: 100%;
  padding: 0 50px;
  color: var(--clr-common-heading);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-tab .nav-tabs .nav-link {
    padding: 0 30px;
  }
}
.about-tab .nav-tabs .nav-link.active {
  background: var(--clr-theme-primary);
  color: var(--clr-common-white);
}

.about-thumb-5 {
  position: absolute;
  height: 100%;
  width: 500%;
  top: 0;
  left: 0;
  width: 50%;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .about-thumb-5 {
    width: 100%;
    position: static;
  }
}
.about-thumb-5 img {
  width: 100%;
  height: 100%;
}

.about-info-wrapper {
  display: inline-flex;
  align-items: center;
  background: rgb(3, 175, 229);
  padding: 25px 30px;
  gap: 30px 80px;
}

.about-info p {
  margin-bottom: 0;
  color: var(--clr-common-white);
}
.about-info h2 {
  display: inline-block;
  font-size: 40px;
  color: var(--clr-common-white);
}
@media (max-width: 575px) {
  .about-info h2 {
    font-size: 30px;
  }
}

.about-content-5 {
  padding: 100px 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-content-5 {
    padding-right: 0;
    padding-left: 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-content-5 {
    padding: 60px;
    padding-right: 0;
    padding-left: 0px;
  }
}
.about-content-5 > p {
  margin-bottom: 35px;
}

.about-thumb-wrapper-6 {
  padding-left: 50px;
}

.about-content-6 {
  padding-right: 60px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .about-content-6 {
    padding-right: 0;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .about-content-6 {
    padding: 50px;
    padding-right: 0px;
    padding-left: 30px;
  }
}
.about-content-6 > p {
  margin-bottom: 35px;
}

.about-experience-2 {
  transform: rotate(-90deg);
  transform-origin: top;
  background: var(--clr-theme-primary);
  padding: 10px 20px;
  text-align: center;
  display: inline-block;
  position: absolute;
  top: 50%;
  right: -32px;
  border: 5px solid var(--clr-common-white);
}
.about-experience-2 p {
  margin-bottom: 0;
  color: var(--clr-common-white);
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
}
@media (max-width: 575px) {
  .about-experience-2 p {
    font-size: 18px;
  }
}

.about-author-thumb {
  max-width: 100px;
}
.about-author-thumb img {
  width: 100%;
  border-radius: 50%;
}

.about-author-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 20px 40px;
  background: rgba(3, 175, 229, 0.05);
  padding: 20px 25px;
}
@media (max-width: 575px) {
  .about-author-wrapper {
    flex-wrap: wrap;
  }
}

.about-author-content h4 {
  font-size: 18px;
  margin-bottom: 5px;
}

.about-author {
  display: flex;
  align-items: center;
  gap: 15px;
}

.about-author-signature {
  border-left: 1px solid var(--clr-border-2);
  padding-left: 15px;
}

/*----------------------------------------*/
/*  SERVICE CSS START
/*----------------------------------------*/
.service-item {
  overflow: hidden;
}
.service-item:hover .service-thumb {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
.service-item:hover .service-icon span {
  background-color: var(--clr-theme-secondary);
}

.service-icon {
  position: absolute;
  top: -50px;
  left: 25px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-icon {
    top: -35px;
  }
}
@media (max-width: 575px) {
  .service-icon {
    left: 20px;
  }
}
.service-icon span {
  height: 100px;
  width: 100px;
  background: var(--clr-theme-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  border: 5px solid var(--clr-common-white);
}
@media (max-width: 575px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-icon span {
    height: 80px;
    width: 80px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-icon span img {
    max-width: 40px;
  }
}

.service-content {
  position: relative;
  background: var(--clr-common-white);
  padding: 70px 30px 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .service-content {
    padding: 70px 25px 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-content {
    padding: 65px 25px 25px;
  }
}
@media (max-width: 575px) {
  .service-content {
    padding: 60px 20px 30px;
  }
}
.service-content h4 {
  margin-bottom: 15px;
  text-transform: capitalize;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-content h4 {
    font-size: 24px;
  }
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-content h4 {
    font-size: 20px;
  }
}
.service-content h4 a:hover {
  color: var(--clr-theme-secondary);
}
.service-content p {
  margin-bottom: 0;
}

.service-link {
  position: absolute;
  right: 30px;
  top: -25px;
}
@media (max-width: 575px) {
  .service-link {
    right: 20px;
  }
}
.service-link a {
  height: 50px;
  width: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-common-white);
  font-size: 20px;
  border: 1px solid rgba(85, 85, 85, 0.2);
}
.service-link a:hover {
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
  border-color: transparent;
}

.service-bg-thumb {
  width: 100%;
  height: 100%;
  mix-blend-mode: soft-light;
  opacity: 60%;
}

.service-bg {
  background: var(--clr-theme-primary);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.service-title-wrapper {
  padding-top: 115px;
  padding-bottom: 60px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-title-wrapper {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.service-title {
  font-size: 120px;
  color: var(--clr-common-white);
  line-height: 1;
  display: inline-block;
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .service-title {
    font-size: 100px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .service-title {
    font-size: 82px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-title {
    font-size: 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service-title {
    font-size: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .service-title {
    font-size: 60px;
  }
}
@media (max-width: 575px) {
  .service-title {
    font-size: 50px;
  }
}
@media (max-width: 450px) {
  .service-title {
    font-size: 36px;
  }
}

.title-wrapper {
  display: grid;
  grid-template-columns: 21% auto;
  align-items: center;
  margin-right: -100%;
  margin-bottom: 40px;
  grid-gap: 10px 30px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .title-wrapper {
    grid-template-columns: auto;
  }
}
.title-wrapper > p {
  color: var(--clr-common-white);
}

.health-icon-2 {
  display: inline-block;
  margin-left: 30px;
  top: -30px;
  position: relative;
}
.health-icon-2 span {
  width: 90px;
  height: 130px;
  background: var(--clr-common-white);
  border-radius: 41.81px;
  display: inline-block;
  text-align: center;
  line-height: 130px;
}

.service-btn-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 25px 20px;
  flex-wrap: wrap;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .service-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .service-grid {
    grid-template-columns: 1fr;
  }
}

.service-item-2 {
  padding: 80px 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.4);
  border-right: 1px solid rgba(255, 255, 255, 0.4);
  position: relative;
  z-index: 5;
  transition: 0.5s;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 1400px) and (max-width: 1600px) {
  .service-item-2 {
    padding: 50px 30px;
  }
}
.service-item-2:hover {
  border-color: transparent;
  background-color: var(--clr-common-white);
}
.service-item-2:hover .service-bottom-content {
  opacity: 1;
  visibility: visible;
}
.service-item-2:hover .service-icon-2 span img:nth-child(1) {
  display: none;
}
.service-item-2:hover .service-icon-2 span img:nth-child(2) {
  display: block;
}
.service-item-2:hover .service-title-2 {
  color: var(--clr-common-heading);
}
.service-item-2:hover .service__thumb-2 {
  opacity: 18%;
  visibility: visible;
}

.service__thumb-2 {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
}

.service-title-2 {
  color: var(--clr-common-white);
  margin-bottom: 30px;
}
.service-title-2:hover a {
  color: var(--clr-theme-primary);
}
@media only screen and (min-width: 1400px) and (max-width: 1600px) {
  .service-title-2 {
    font-size: 22px;
  }
}
@media (max-width: 450px) {
  .service-title-2 {
    font-size: 20px;
  }
}

.service-bottom-content {
  opacity: 0;
  visibility: hidden;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-bottom-content {
    opacity: 1;
    visibility: visible;
  }
}
.service-bottom-content P {
  color: var(--clr-text-body);
  margin-bottom: 40px;
}

.service-icon-2 span img:nth-child(2) {
  display: none;
}

.service-wrapper {
  min-height: 620px;
  position: relative;
  z-index: 5;
  overflow: hidden;
}
.service-wrapper:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: var(--clr-theme-primary);
  opacity: 0.7;
  z-index: -1;
  top: 0;
  left: 0;
}

.service-bg-thumb-3 {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.service-icon-3 {
  margin-bottom: 25px;
  margin-bottom: 25px;
}
.service-icon-3 span {
  background: #161616;
  display: inline-block;
  height: 87px;
  width: 87px;
  align-items: center;
  text-align: center;
  line-height: 87px;
  border-radius: 50%;
}

.service-content-3 h4 {
  color: var(--clr-common-white);
  margin-bottom: 20px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-content-3 h4 {
    font-size: 20px;
  }
}
.service-content-3 h4 a {
  position: relative;
}
.service-content-3 h4 a:after {
  position: absolute;
  content: "";
  left: auto;
  bottom: 0;
  background: currentColor;
  width: 0;
  height: 1px;
  transition: 0.3s;
  right: 0;
}
.service-content-3 h4 a:hover {
  color: var(--clr-common-white);
}
.service-content-3 h4 a:hover::after {
  width: 100%;
  left: 0;
  right: auto;
}
.service-content-3 p {
  color: var(--clr-common-white);
}

.service-link-3 {
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
  color: var(--clr-common-white);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  position: relative;
}
.service-link-3:after {
  position: absolute;
  content: "";
  left: auto;
  bottom: 0;
  background: currentColor;
  width: 0;
  height: 1px;
  transition: 0.3s;
  right: 0;
}
.service-link-3:hover {
  color: var(--clr-common-white);
}
.service-link-3:hover::after {
  width: 100%;
  left: 0;
  right: auto;
}

.service-item-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-item-grid {
    display: flex;
    flex-direction: column;
    position: inherit;
  }
}

.service-item-3 {
  transform: translateY(130px);
  padding: 40px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-item-3 {
    padding: 30px 20px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-item-3 {
    transform: translateY(0px);
  }
}
.service-item-3:hover {
  transform: translateY(0px);
}
.service-item-3:hover .service-icon-3 span {
  background-color: var(--clr-theme-secondary);
}

.service-line-1 {
  height: 100%;
  width: 1px;
  display: inline-flex;
  background: rgba(255, 255, 255, 0.2);
  position: absolute;
  top: 0;
  left: 33.33%;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-line-1 {
    display: none;
  }
}

.service-line-2 {
  height: 100%;
  width: 1px;
  display: inline-flex;
  background: rgba(255, 255, 255, 0.2);
  position: absolute;
  top: 0;
  right: 33.33%;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .service-line-2 {
    display: none;
  }
}

.service-item-4 {
  background: rgb(255, 255, 255);
  padding: 30px 30px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-item-4 {
    padding: 20px 20px;
  }
}
.service-item-4:hover .service-thumb-4 {
  margin-bottom: 30px;
  position: relative;
}
.service-item-4:hover .service-thumb-4:before {
  opacity: 1;
}
.service-item-4:hover .service-link-2 {
  opacity: 1;
  visibility: visible;
}

.service-thumb-4 {
  margin-bottom: 30px;
  position: relative;
}
.service-thumb-4:before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: rgba(3, 175, 229, 0.69);
  opacity: 0;
}
.service-thumb-4 img {
  width: 100%;
}

.service-content-4 h3 {
  font-size: 24px;
  margin-bottom: 10px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .service-content-4 h3 {
    font-size: 20px;
  }
}
.service-content-4 h3 a:hover {
  color: var(--clr-theme-secondary);
}
.service-content-4 p {
  margin-bottom: 0;
}

.service-link-2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  visibility: hidden;
}
.service-link-2 a {
  height: 60px;
  width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-theme-secondary);
  font-size: 20px;
  border-radius: 50%;
  color: var(--clr-common-white);
}

/*----------------------------------------*/
/* APPOINTMENT CSS START
/*----------------------------------------*/
.appointment-input-wrapper-2 .contact__from-input input,
.appointment-input-wrapper-2 .contact__from-input textarea {
  background: rgba(3, 175, 229, 0.1);
}
.appointment-input-wrapper-2 .contact__select select {
  background: rgba(3, 175, 229, 0.1);
}

.appointment-content {
  padding: 120px 0;
  padding-left: 90px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .appointment-content {
    padding-left: 60px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .appointment-content {
    padding: 100px 0;
    padding-left: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .appointment-content {
    padding: 100px 0 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .appointment-content {
    padding: 80px 0px;
  }
}
@media (max-width: 575px) {
  .appointment-content {
    padding: 70px 0;
  }
}
.appointment-content p {
  color: var(--clr-common-white);
  margin-bottom: 0;
}

.appointment-thumb-box {
  position: absolute;
  top: 0;
  height: 100%;
  width: 50%;
  left: 0;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .appointment-thumb-box {
    position: relative;
    width: 100%;
  }
}

.appointment-thumb {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
  left: 0;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .appointment-thumb {
    position: inherit;
  }
}

.appointment-video-shape {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  left: 50%;
  text-align: center;
  border-radius: 50%;
}

.appointment-thumb-2 {
  position: absolute;
  top: 0;
  height: 100%;
  width: 50%;
  left: 0;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .appointment-thumb-2 {
    position: inherit;
    width: 100%;
    padding-top: 100px;
  }
}

.appointment-bg {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 50%;
  background-repeat: no-repeat;
  opacity: 50%;
  background-size: cover;
  background-position: center;
  z-index: -1;
}

.appointment-input-wrapper {
  padding-left: 30px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .appointment-input-wrapper {
    padding-left: 0;
  }
}

.appointment-input-wrapper .contact__from-input textarea {
  min-height: 160px;
}

.appointment-input-wrapper-3 {
  background: #edf9fd;
  padding: 40px 30px;
  margin-right: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .appointment-input-wrapper-3 {
    margin-right: 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .appointment-input-wrapper-3 {
    margin-right: 0px;
  }
}
@media (max-width: 450px) {
  .appointment-input-wrapper-3 {
    padding: 30px 20px;
  }
}

.appointment-input-title {
  margin-bottom: 25px;
  color: var(--clr-theme-primary);
}

.appointment-content-3 > p {
  margin-bottom: 30px;
}

.appointment-contact {
  display: grid;
  grid-template-columns: auto auto;
  gap: 30px 30px;
  margin-bottom: 30px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .appointment-contact {
    grid-template-columns: auto;
  }
}

.appointment-contact-icon span {
  font-size: 26px;
  width: 60px;
  height: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-theme-primary);
  border-radius: 50%;
  color: var(--clr-common-white);
  margin-bottom: 20px;
}

.appointment-contact-info {
  background: rgba(2, 175, 229, 0.05);
  text-align: center;
  padding: 20px;
}
.appointment-contact-info h6 {
  font-size: 16px;
}
.appointment-contact-info h6 a:hover {
  color: var(--clr-theme-secondary);
}

.appointment-map iframe {
  height: 275px;
  width: 100%;
  filter: grayscale(1);
}

.appointment-thumb-3 img {
  border-radius: 50%;
}

.appointment-input-wrapper-2 {
  background: var(--clr-common-white);
  padding: 50px 50px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .appointment-input-wrapper-2 {
    padding: 30px 30px;
  }
}
@media (max-width: 575px) {
  .appointment-input-wrapper-2 {
    padding: 30px 20px;
  }
}

.appointment-thumb-3 {
  padding-left: 30px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .appointment-thumb-3 {
    padding-left: 0;
  }
}

/*----------------------------------------*/
/*  TEAM CSS START
/*----------------------------------------*/
.team-item .team-thumb {
  max-width: 158px;
  margin: auto auto;
  position: relative;
  z-index: 5;
  border: 10px solid var(--clr-common-white);
  border-radius: 50%;
}
.team-item .team-thumb img {
  border-radius: 50%;
  width: 100%;
}
.team-item .team-thumb img img {
  transform: scale(1.1);
}
.team-item:hover .team-content {
  background: rgba(235, 117, 59, 0.1);
}
.team-item:hover .team-thumb img {
  transform: scale(1.1);
}

.team-content {
  background: var(--clr-bg-4);
  padding: 115px 30px 50px;
  margin-top: -90px;
}
@media (max-width: 450px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .team-content {
    padding: 115px 30px 30px;
  }
}

.team-title {
  margin-bottom: 10px;
  font-size: 24px;
}
.team-title a:hover {
  color: var(--clr-theme-secondary);
}

.team-content span {
  display: block;
  margin-bottom: 20px;
  color: var(--clr-theme-secondary);
}

.team-content p {
  margin-bottom: 25px;
}

.team-thumb-box {
  padding-right: 80px;
}

.doctor-tool-shapes {
  position: absolute;
  top: 110px;
  right: 58px;
}

.team-content-2 {
  padding-right: 100px;
}

.intro-bg-thumb {
  width: 100%;
  height: 100%;
  mix-blend-mode: soft-light;
  opacity: 60%;
}

.intro-bg {
  background: var(--clr-theme-primary);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.team-thumb-box {
  padding-right: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .team-thumb-box {
    padding-right: 50px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .team-thumb-box {
    padding-right: 0;
  }
}

.doctor-tool-shapes {
  position: absolute;
  top: 110px;
  right: 58px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .doctor-tool-shapes {
    top: 40px;
    right: 20px;
  }
  .doctor-tool-shapes img {
    max-width: 100px;
  }
}

.team-content-2 {
  padding-right: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .team-content-2 {
    padding-right: 50px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .team-content-2 {
    padding-right: 0;
  }
}

.team-card-content h4 {
  margin-bottom: 5px;
  color: var(--clr-common-white);
  font-size: 20px;
}
@media (max-width: 450px) {
  .team-card-content h4 {
    font-size: 18px;
  }
}
.team-card-content span {
  color: var(--clr-common-white);
}

.team-card {
  position: absolute;
  bottom: 25px;
  left: 140px;
  background: var(--clr-theme-primary);
  padding: 20px 20px;
}

.team-thumb-radius img {
  border-radius: 160px 160px 0 0;
}
@media (max-width: 575px) {
  .team-thumb-radius img {
    border-radius: 0;
  }
}

.team-social a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--clr-common-white);
  border-radius: 50%;
  color: rgb(22, 22, 22);
  border: 1px solid #dce4e7;
}
.team-social a:hover {
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
  border-color: transparent;
}

/*----------------------------------------*/
/* BLOG CSS START
/*----------------------------------------*/
.postbox__meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.postbox__meta span {
  position: relative;
  font-size: 14px;
}
.postbox__meta span a:hover {
  color: var(--clr-theme-secondary);
}

.postbox__tag-unfill {
  padding: 7px 10px;
  border: 1px solid var(--clr-border-2);
  line-height: 1;
  display: inline-block;
  font-size: 14px;
  border-radius: 50px;
}
.postbox__tag-unfill:hover {
  border-color: transparent;
  background-color: var(--clr-theme-primary);
  color: var(--clr-common-white);
}

.blog-item {
  padding: 30px;
}
@media (max-width: 575px) {
  .blog-item {
    padding: 25px 20px;
  }
}
.blog-item:hover .blog-thumb img {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.blog-thumb {
  position: relative;
  overflow: hidden;
}
.blog-thumb img {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.blog-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 1;
}
.blog-badge span {
  font-size: 14px;
  color: var(--clr-common-white);
  background-color: var(--clr-theme-primary);
  padding: 8px 15px;
  border-radius: 30px;
}

.blog-title {
  margin-bottom: 25px;
}
@media (max-width: 575px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-title {
    font-size: 24px;
  }
}
@media (max-width: 450px) {
  .blog-title {
    font-size: 22px;
  }
}
.blog-title a:hover {
  color: var(--clr-theme-secondary);
}

.blog-content-wrapper {
  display: grid;
  gap: 30px;
}

.blog-content-box {
  position: relative;
  padding: 40px 35px;
  border: 1px solid var(--clr-border-2);
}
@media (max-width: 450px) {
  .blog-content-box {
    padding: 30px 20px;
  }
}
.blog-content-box:hover {
  background: var(--clr-common-white);
  border-color: transparent;
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.08);
}
.blog-content-box:hover .blog-content-thumb {
  opacity: 1;
  visibility: visible;
}

.blog-content-item {
  display: grid;
  grid-template-columns: auto auto;
  row-gap: 30px;
  column-gap: 30px;
  align-items: center;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-content-item {
    grid-template-columns: auto;
  }
}

.blog-content-thumb {
  position: absolute;
  width: 301px;
  top: 50%;
  transform: translateY(-50%);
  right: 155px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-content-thumb {
    position: inherit;
    width: 100%;
    top: inherit;
    transform: inherit;
    opacity: 1;
    visibility: visible;
    margin-top: 30px;
  }
}
.blog-content-thumb img {
  width: 100%;
}

.blog-content-title {
  font-size: 22px;
  line-height: 1.5;
}
@media (max-width: 450px) {
  .blog-content-title {
    font-size: 20px;
  }
}
.blog-content-title a:hover {
  color: var(--clr-theme-primary);
}

.blog-tag {
  display: inline-flex;
  background: rgba(3, 175, 229, 0.15);
  text-align: left;
  padding: 8px 10px;
  line-height: 1;
  border-radius: 1000px;
  font-size: 14px;
  color: var(--clr-theme-primary);
}

.blog-item-3 {
  border: 1px solid rgba(22, 22, 22, 0.1);
  padding: 35px 30px;
  position: relative;
  z-index: 5;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-item-3 {
    padding: 30px 20px;
  }
}
.blog-item-3:hover {
  border-color: transparent;
}
.blog-item-3:hover .blog-content-3 h4 {
  color: var(--clr-common-white);
}
.blog-item-3:hover .postbox__meta span {
  color: var(--clr-common-white);
}
.blog-item-3:hover .divider {
  border-color: rgba(255, 255, 255, 0.1);
}
.blog-item-3:hover .blog-tag {
  color: #cac8c8;
}
.blog-item-3:hover .blog-thumb-bg {
  opacity: 1;
}
.blog-item-3 .divider {
  padding-top: 30px;
  border-bottom: 1px solid var(--clr-border-2);
  display: block;
  margin-bottom: 30px;
}

.blog-thumb-bg {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  opacity: 0;
}
.blog-thumb-bg::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: rgba(22, 22, 22, 0.8);
  top: 0;
  left: 0;
}

.blog-content-3 h4 {
  margin-bottom: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-content-3 h4 {
    font-size: 24px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .blog-content-3 h4 {
    font-size: 24px;
    margin-bottom: 30px;
  }
}
.blog-content-3 h4 a {
  background-image: linear-gradient(#fff, #fff), linear-gradient(#fff, #fff);
  display: inline;
  background-size: 0% 1px, 0 1px;
  background-position: 100% 100%, 0 80%;
  background-repeat: no-repeat;
  transition: background-size 0.4s linear;
}
.blog-content-3 h4 a:hover {
  background-size: 0 1px, 100% 1px;
  color: var(--clr-common-white);
}

.blog-item-5 {
  padding: 20px 20px 30px;
  background: rgb(246, 246, 246);
}
.blog-item-5:hover .blog-item-thumb-5 img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

.blog-item-thumb-5 {
  margin-bottom: 30px;
  overflow: hidden;
}

.blog-title-5 {
  font-size: 22px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .blog-title-5 {
    font-size: 20px;
  }
}
.blog-title-5 a:hover {
  color: var(--clr-theme-secondary);
}

.postbox__wrapper {
  padding: 30px 30px;
  background: #f6f6f6;
}
@media (max-width: 450px) {
  .postbox__wrapper {
    padding: 20px 20px;
  }
}

.postbox__title {
  font-size: 36px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .postbox__title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .postbox__title {
    font-size: 30px;
  }
}
@media (max-width: 575px) {
  .postbox__title {
    font-size: 24px;
  }
}
.postbox__title:hover {
  color: var(--clr-theme-secondary);
}

.postbox__meta {
  margin-bottom: 7px;
}
.postbox__meta span {
  font-size: 14px;
  color: var(--clr-text-4);
  margin-right: 15px;
  font-weight: 500;
  display: inline-block;
  margin-bottom: 5px;
}

.postbox__text p {
  margin-bottom: 20px;
}

.postbox__meta span svg,
.postbox__meta span i {
  margin-right: 5px;
}

.postbox__video .play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  font-size: 20px;
}

.blog__grid-quote {
  padding: 70px 40px 67px;
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.1);
  background-color: var(--bd-common-white);
}

.blog__grid-quote-icon span {
  display: inline-block;
  font-size: 24px;
  color: var(--bd-theme-1);
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 56px;
  border: 2px solid #eff0f6;
  border-radius: 50%;
}

.blog__grid-quote-content span {
  font-size: 14px;
  color: #525258;
  display: inline-block;
  margin-bottom: 15px;
}
.blog__grid-quote-content cite {
  font-family: var(--bd-ff-space);
  font-weight: 500;
  font-size: 18px;
  color: var(--bd-common-black-solid);
  font-style: normal;
  position: relative;
  padding-top: 12px;
}
.blog__grid-quote-content cite::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 30px;
  height: 2px;
  background-color: var(--bd-theme-1);
}

.postbox__quote p {
  font-family: var(--bd-ff-space);
  font-weight: 500;
  font-size: 22px;
  line-height: 1.36;
  letter-spacing: -0.02em;
  color: var(--bd-common-black);
  margin-bottom: 0;
}

.postbox__nav button {
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-common-white);
  position: absolute;
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
  z-index: 1;
  font-size: 18px;
}
.postbox__nav button:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}
.postbox__nav button.postbox-slider-button-next {
  right: 15px;
  left: auto;
}

@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .sidebar__wrapper {
    padding: 0;
  }
}

.sidebar__widget-title {
  position: relative;
  display: inline-block;
  font-weight: 500;
  font-size: 22px;
  margin-bottom: 23px;
}

.sidebar__search {
  position: relative;
}
.sidebar__search button {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  padding: 0 24px;
  color: var(--bd-common-black);
  line-height: 60px;
  background-color: var(--bd-common-white);
}

.sidebar__search-input input {
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding-left: 25px;
  padding-right: 50px;
  font-size: 15px;
}
.sidebar__search-input input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  font-size: 15px;
}
.sidebar__search-input input::-moz-placeholder {
  /* Firefox 19+ */
  font-size: 15px;
}
.sidebar__search-input input:-moz-placeholder {
  /* Firefox 4-18 */
  font-size: 15px;
}
.sidebar__search-input input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  font-size: 15px;
}
.sidebar__search-input input::placeholder {
  /* MODERN BROWSER */
  font-size: 15px;
}

.sidebar__author {
  text-align: center;
}

.sidebar__author-thumb {
  margin-bottom: 20px;
}
.sidebar__author-thumb img {
  width: 164px;
  height: 164px;
  border-radius: 50%;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.sidebar__author-title {
  font-size: 24px;
  margin-bottom: 14px;
}

.sidebar__author-content p {
  font-size: 15px;
  line-height: 1.47;
  margin-bottom: 22px;
}

.sidebar__author-social a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  border: 1px solid var(--clr-border-2);
  margin-right: 3px;
}
.sidebar__author-social a:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}

.sidebar__widget {
  background: rgb(246, 246, 246);
  padding: 30px 30px;
}
@media (max-width: 450px) {
  .sidebar__widget {
    padding: 20px 20px;
  }
}
.sidebar__widget ul li {
  list-style: none;
  font-size: 12px;
  margin-bottom: 10px;
}
.sidebar__widget ul li a {
  font-weight: 500;
  font-size: 16px;
  position: relative;
  background: var(--clr-common-white);
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.06);
  display: block;
  padding: 15px 14px;
  padding-left: 30px;
}
.sidebar__widget ul li a:hover {
  color: var(--clr-theme-secondary);
}
.sidebar__widget ul li a::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 20px;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: var(--clr-common-black);
  border-radius: 50%;
}
.sidebar__widget ul li a span {
  float: right;
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  padding: 4px 11px 5px;
  border: 1px solid #eaeaef;
  border-radius: 20px;
  margin-top: 2px;
}

.rc__post {
  padding: 12px;
  background-color: var(--clr-common-white);
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.06);
  margin-bottom: 20px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .rc__post {
    flex-wrap: wrap;
    gap: 15px;
  }
}
.rc__post:hover .rc__post-thumb img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

.rc__post-thumb {
  overflow: hidden;
  margin-right: 25px;
  flex: 0 0 auto;
}
.rc__post-thumb img {
  width: 90px;
  height: 90px;
  object-fit: cover;
  width: 90px;
  height: 90px;
  object-fit: cover;
}

.rc__post-title {
  font-size: 16px;
  line-height: 1.25;
}
.rc__post-title:hover {
  color: var(--clr-theme-secondary);
}

.rc__meta span {
  font-size: 14px;
}
.rc__meta span svg {
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
}

.rc__meta span svg,
.rc__meta span i {
  margin-right: 6px;
}

.tagcloud a {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
  color: var(--clr-text-body);
  padding: 8px 16px;
  text-transform: capitalize;
  margin-bottom: 6px;
  margin-right: 2px;
  border: 1px solid rgba(133, 133, 133, 0.2);
}
.tagcloud a:hover {
  background: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}

.postbox__features-title {
  font-size: 28px;
}

.features__list ul li {
  font-size: 16px;
  position: relative;
  list-style: none;
  padding-left: 35px;
  font-weight: 500;
}
.features__list ul li:not(:last-child) {
  margin-bottom: 15px;
}
.features__list ul li::after {
  position: absolute;
  content: "\e09f";
  height: 24px;
  width: 24px;
  top: 50%;
  left: 0;
  background: var(--clr-theme-secondary);
  transform: translateY(-50%);
  font-family: var(--bd-ff-fontawesome);
  text-align: center;
  border-radius: 50%;
  color: var(--clr-common-white);
  font-size: 14px;
}

.postbox__quote {
  margin-top: 40px;
  border: 1px solid var(--clr-border-2);
}
.postbox__quote blockquote {
  padding: 30px 35px 25px;
  margin: 0;
}
.postbox__quote blockquote p {
  margin-bottom: 20px;
}

.quote__author-info h4 {
  font-size: 20px;
}

.tagcloud span {
  font-weight: 500;
  font-size: 16px;
  margin-right: 12px;
}

.postbox__share span {
  font-weight: 500;
  font-size: 16px;
  margin-right: 12px;
}
.postbox__share a {
  display: inline-block;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  margin-bottom: 6px;
  border: 1px solid var(--clr-border-2);
}
.postbox__share a:not(:first-child) {
  margin-left: 5px;
}
.postbox__share a:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}

.postbox__comment ul li {
  margin-bottom: 20px;
  list-style: none;
}
.postbox__comment ul.children {
  margin-top: 20px;
  margin-left: 100px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .postbox__comment ul.children {
    margin-left: 40px;
  }
}
@media (max-width: 575px) {
  .postbox__comment ul.children {
    margin-left: 15px;
  }
}
.postbox__comment-form-title {
  font-size: 32px;
  letter-spacing: -0.02em;
  margin-bottom: 15px;
}
.postbox__comment-form p {
  font-size: 14px;
  margin-bottom: 40px;
}
.postbox__comment-input {
  position: relative;
  margin-bottom: 20px;
}
.postbox__comment-input span {
  font-weight: 600;
  margin-bottom: 12px;
  display: block;
}
.postbox__comment-input input,
.postbox__comment-input textarea {
  height: 60px;
  padding: 0 25px;
  width: 100%;
  font-size: 14px;
  color: var(--clr-common-black);
  border-color: var(--clr-common-white);
  background: var(--clr-common-white);
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.06);
  border-width: 1px;
}
.postbox__comment-input input:focus,
.postbox__comment-input textarea:focus {
  border-color: var(--clr-theme-secondary);
}
.postbox__comment-input textarea {
  height: 180px;
  resize: none;
  padding-top: 0;
  padding-bottom: 25px;
}
.postbox__comment-title {
  font-size: 28px;
  letter-spacing: -0.02em;
  margin-bottom: 30px;
}
.postbox__comment-box {
  padding: 30px;
  padding-right: 85px;
  padding-bottom: 33px;
  background: var(--clr-common-white);
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.06);
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .postbox__comment-box {
    padding-right: 35px;
  }
}
@media (max-width: 575px) {
  .postbox__comment-box {
    padding-right: 35px;
  }
}
.postbox__comment-info {
  flex: 0 0 auto;
}
.postbox__comment-avatar img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}
.postbox__comment-name {
  margin-bottom: 5px;
}
.postbox__comment-name h5 {
  font-size: 20px;
  line-height: 1;
  margin-bottom: 10px;
}
.postbox__comment-name span {
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0.02em;
  margin-bottom: 10px;
  display: block;
}
@media (max-width: 575px) {
  .postbox__comment-text {
    margin-left: 0;
    margin-top: 15px;
  }
}
.postbox__comment-text p {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 22px;
}
.postbox__comment-reply {
  margin-top: 10px;
}
.postbox__comment-reply a {
  display: inline-block;
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  letter-spacing: -0.02em;
  border: 1px solid #eaeaef;
  padding: 7px 18px;
}
.postbox__comment-reply a:hover {
  color: var(--clr-common-white);
  background: var(--clr-theme-secondary);
  border-color: transparent;
}
.postbox__comment-agree {
  padding-left: 5px;
}
.postbox__comment-agree input {
  margin: 0;
  appearance: none;
  -moz-appearance: none;
  display: block;
  width: 18px;
  height: 18px;
  background: #f0f0f5;
  border: 1px solid #c5c5ce;
  outline: none;
  flex: 0 0 auto;
  -webkit-transform: translateY(-2.5px);
  -moz-transform: translateY(-2.5px);
  -ms-transform: translateY(-2.5px);
  -o-transform: translateY(-2.5px);
  transform: translateY(-2.5px);
  padding: 0;
}
.postbox__comment-agree input:checked {
  position: relative;
  background-color: var(--clr-theme-secondary);
  border-color: transparent;
}
.postbox__comment-agree input:checked::after {
  box-sizing: border-box;
  position: absolute;
  content: "\f00c";
  font-weight: 700;
  font-family: var(--bd-ff-fontawesome);
  font-size: 12px;
  color: var(--clr-common-white);
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.postbox__comment-agree input:hover {
  cursor: pointer;
}
.postbox__comment-agree label {
  padding-left: 8px;
  font-size: 14px;
  color: var(--clr-text-body);
  line-height: 1.3;
}
.postbox__comment-agree label a {
  font-weight: 600;
  padding-left: 4px;
}
.postbox__comment-agree label a:hover {
  color: var(--clr-theme-primary);
}
.postbox__comment-agree label:hover {
  cursor: pointer;
}

/*----------------------------------------*/
/*  PROJECT CSS START
/*----------------------------------------*/
.project-item {
  position: relative;
}
.project-item:before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: linear-gradient(0deg, #eb753b 0%, rgba(235, 117, 59, 0) 100%);
  top: 0;
  left: 0;
  opacity: 0;
}
.project-item:hover .project-content-inner {
  opacity: 1;
  visibility: visible;
  top: auto;
  bottom: 0;
}
.project-item:hover::before {
  opacity: 1;
}

.project-content-inner {
  display: flex;
  align-items: end;
  justify-content: space-between;
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: -50px;
  left: 0;
  padding: 30px 30px;
  opacity: 0;
  visibility: hidden;
}
@media (max-width: 450px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .project-content-inner {
    padding: 25px 25px;
  }
}

.project-content span {
  margin-bottom: 5px;
  display: block;
  color: var(--clr-common-white);
}
.project-content h4 {
  font-size: 22px;
  color: var(--clr-common-white);
}
@media (max-width: 450px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .project-content h4 {
    font-size: 20px;
  }
}

.swiper.project-active {
  margin-right: -300px;
}
@media (max-width: 575px) {
  .swiper.project-active {
    margin-right: 0;
  }
}

.project-details-bg {
  position: absolute;
  height: 525px;
  width: 100%;
  background: var(--clr-bg-1);
  bottom: 0;
  left: 0;
  z-index: -1;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .project-details-bg {
    height: 350px;
  }
}
@media (max-width: 575px) {
  .project-details-bg {
    height: 100%;
  }
}

.portfolio-meta-item {
  display: flex;
  align-items: start;
  gap: 10px;
  justify-content: center;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .portfolio-meta-item {
    justify-content: start;
  }
}

.portfolio-meta-icon span {
  width: 50px;
  height: 50px;
  background: var(--clr-theme-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.portfolio-meta-content p {
  margin-bottom: 0;
}
.portfolio-meta-content h4 {
  font-size: 20px;
  margin-bottom: 5px;
}

/*----------------------------------------*/
/*  HEADER CSS START
/*----------------------------------------*/
.why-choose-inner {
  padding-right: 20px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-inner {
    padding-right: 0;
  }
}

.why-choose-inner-2 {
  padding-left: 20px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-inner-2 {
    padding-left: 0;
  }
}

.why-choose-item {
  padding: 30px 30px 25px;
  position: relative;
}
.why-choose-item.active {
  background: rgba(3, 175, 229, 0.1);
}
.why-choose-item.active .why-choose-serial span {
  opacity: 1;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .why-choose-item {
    background: rgba(3, 175, 229, 0.1);
  }
  .why-choose-item .why-choose-serial span {
    opacity: 1;
  }
}

.why-choose-serial {
  position: absolute;
  right: 30px;
  top: 30px;
}
.why-choose-serial span {
  font-weight: 700;
  font-size: 65px;
  line-height: 1;
  color: transparent;
  -webkit-text-stroke: 1px rgba(3, 175, 229, 0.2);
  opacity: 0;
}

.why-choose-content p {
  margin-bottom: 0;
}
.why-choose-content h4 {
  margin-bottom: 25px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-content h4 {
    font-size: 20px;
  }
}
.why-choose-content h4 a:hover {
  color: var(--clr-theme-primary);
}

.why-choose-icon {
  margin-bottom: 25px;
}
.why-choose-icon span {
  width: 71.4px;
  height: 71.4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-theme-primary);
  border-radius: 50%;
  position: relative;
}

.why-choose-content-2 {
  padding: 0 50px;
}

.why-choose-icon-2 span {
  width: 100px;
  height: 100px;
  display: inline-block;
  background: #d9f3fb;
  line-height: 100px;
  border-radius: 50%;
  margin-bottom: 25px;
  position: relative;
}
.why-choose-icon-2 span span {
  font-size: 16px;
  position: absolute;
  right: 0;
  top: 0;
  width: 30px;
  height: 30px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-common-white);
  border-radius: 50%;
  color: var(--clr-theme-secondary);
}

.why-choose-content-2 h4 {
  margin-bottom: 20px;
}
@media (max-width: 575px) {
  .why-choose-content-2 h4 {
    margin-bottom: 15px;
    font-size: 19px;
  }
}
.why-choose-content-2 p {
  margin-bottom: 0;
}

.why-choose-item {
  background-color: var(--clr-common-white);
  padding: 40px 40px 35px;
}
@media (max-width: 450px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-item {
    padding: 30px 30px;
  }
}

.why-choose-icon {
  margin-bottom: 20px;
}
.why-choose-icon span {
  width: 70px;
  height: 70px;
  background: var(--clr-theme-secondary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.why-choose-content p {
  margin-bottom: 0;
}
.why-choose-content h4 {
  margin-bottom: 15px;
}
@media (max-width: 450px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 1200px) and (max-width: 1399px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-content h4 {
    font-size: 20px;
  }
}

.why-choose-thumb {
  position: relative;
  padding: 0 10px;
  z-index: 55;
  margin-right: 20px;
}
.why-choose-thumb img {
  width: 100%;
  border-radius: 322px 322px 0 0;
}
.why-choose-thumb:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: rgb(3, 175, 229);
  left: 10px;
  border-radius: 322px 322px 0 0;
  z-index: -1;
  bottom: -10px;
}

/*----------------------------------------*/
/*  SKILL CSS START
/*----------------------------------------*/
.skill-award {
  position: absolute;
  bottom: 80px;
  left: 34px;
}
.skill-award h6 {
  height: 122px;
  width: 122px;
  background: var(--clr-common-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #161616;
  position: relative;
  transform: rotate(-30deg);
}
.skill-award h6:after {
  position: absolute;
  content: "";
  height: 144px;
  width: 144px;
  background: va;
  border: 1px dashed var(--clr-common-white);
  border-radius: 50%;
  -webkit-animation-name: rotateCircle;
  animation-name: rotateCircle;
  -webkit-animation-duration: 20s;
  animation-duration: 20s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.skill-thumb {
  background: var(--clr-theme-primary);
  text-align: center;
}

.skill-content {
  padding: 0 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .skill-content {
    padding: 0 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .skill-content {
    padding: 0;
  }
}
.skill-content > p {
  margin-bottom: 40px;
}

.bd-progress__skill-item {
  position: relative;
}
.bd-progress__skill-item:not(:last-child) {
  margin-bottom: 30px;
}
.bd-progress__skill-item h5 {
  margin-bottom: 10px;
  font-size: 16px;
}
.bd-progress__skill-item .progress-count {
  position: absolute;
  top: 0px;
  right: 0;
  font-weight: 500;
  color: var(--clr-theme-primary);
}
.bd-progress__skill-item .progress {
  height: 10px;
  background-color: transparent;
  border-radius: 0;
  overflow: visible;
  position: relative;
  z-index: 1;
}
.bd-progress__skill-item .progress::after {
  position: absolute;
  content: "";
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 10px;
  width: 100%;
  background-color: #c9edff;
  z-index: -1;
  border-radius: 4px;
}
.bd-progress__skill-item .progress-bar {
  background-color: var(--clr-theme-primary);
  overflow: visible;
  border-radius: 10px;
}

.skill-content-2 {
  padding-right: 100px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .skill-content-2 {
    padding-right: 0;
  }
}

/*----------------------------------------*/
/*  TESTIMONIAL CSS START
/*----------------------------------------*/
.swiper.testimonial-active {
  position: relative;
  margin-right: -540px;
}
@media (max-width: 575px) {
  .swiper.testimonial-active {
    margin-right: 0;
  }
}

.testimonial-item {
  padding: 40px 40px;
  background: var(--clr-common-white);
}
@media (max-width: 575px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .testimonial-item {
    padding: 30px 30px;
  }
}
@media (max-width: 450px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial-item {
    padding: 30px 25px;
  }
}

.avatar-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.avatar-name {
  margin-bottom: 5px;
  font-size: 20px;
}
@media (max-width: 450px) {
  .avatar-name {
    font-size: 18px;
  }
}

.testimonial-icon {
  margin-bottom: 30px;
}

.testimonial-content p {
  margin-bottom: 25px;
  border-bottom: 1px solid rgba(22, 22, 22, 0.1);
  padding-bottom: 25px;
  font-size: 18px;
  line-height: 1.5;
}

.avatar-thumbnail {
  width: 80px;
  height: 80px;
}
.avatar-thumbnail img {
  width: 100%;
  border-radius: 50%;
}

.testimonial-content-2 p {
  color: var(--clr-common-white);
  font-size: 32px;
  line-height: 1.3;
  margin-bottom: 65px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .testimonial-content-2 p {
    font-size: 28px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial-content-2 p {
    font-size: 26px;
    margin-bottom: 45px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .testimonial-content-2 p {
    font-size: 24px;
  }
}
@media (max-width: 450px) {
  .testimonial-content-2 p {
    font-size: 20px;
    margin-bottom: 30px;
  }
}

.avatar-info-2 {
  position: relative;
}
.avatar-info-2:after {
  position: absolute;
  content: "";
  width: 150px;
  height: 1px;
  background: var(--clr-common-white);
  left: -105%;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 575px) {
  .avatar-info-2:after {
    display: none;
  }
}
.avatar-info-2 .avatar-name {
  color: var(--clr-common-white);
}
.avatar-info-2 span {
  color: var(--clr-common-white);
}

.testimonial-bg {
  background: var(--clr-theme-primary);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.testimonial-bg-thumb {
  width: 100%;
  height: 100%;
  mix-blend-mode: soft-light;
  opacity: 40.9%;
}

.testimonial-thumb {
  position: relative;
  max-width: 416px;
  margin: 0 auto;
}
.testimonial-thumb:before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: var(--clr-theme-secondary);
  z-index: -1;
  border-radius: 280px;
  left: -10px;
  bottom: -10px;
}
@media (max-width: 575px) {
  .testimonial-thumb:before {
    border-radius: 0;
    left: 0;
  }
}
.testimonial-thumb img {
  border-radius: 280px;
  width: 100%;
}
@media (max-width: 575px) {
  .testimonial-thumb img {
    border-radius: 0;
  }
}

.testmonial-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.common-slider-navigation {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}
.common-slider-navigation button {
  width: 60px;
  height: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--clr-border-2);
  color: var(--clr-common-heading);
  border-radius: 50%;
}
.common-slider-navigation button:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
  border-color: transparent;
}
.common-slider-navigation.is-white button {
  border-color: var(--clr-common-white);
  color: var(--clr-common-white);
}
.common-slider-navigation.is-white button:hover {
  border-color: transparent;
}

.review-shpes .shape-1 {
  position: absolute;
  top: 0;
  left: 0;
  max-width: 597px;
}
.review-shpes .shape-1 img {
  width: 100%;
}
.review-shpes .shape-2 {
  position: absolute;
  bottom: 0;
  right: 0;
}

.review-item {
  padding: 50px 45px;
  background: var(--clr-common-white);
  position: relative;
}
@media (max-width: 575px) {
  .review-item {
    padding: 30px 25px;
  }
}

.review-active-two.slick-vertical .slick-slide {
  border: 0;
}

.review-content > p {
  margin-bottom: 25px;
  border-bottom: 1px solid var(--clr-border-2);
  padding-bottom: 30px;
  font-size: 20px;
  line-height: 1.5;
}
@media (max-width: 575px) {
  .review-content > p {
    font-size: 18px;
  }
}

.review-intro {
  padding-right: 90px;
  padding-left: 30px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .review-intro {
    padding-right: 0;
    padding-left: 0;
  }
}

.review-active-two {
  position: relative;
}
.review-active-two:before {
  position: absolute;
  content: "";
  height: 10px;
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  top: -10px;
  left: 0;
}
.review-active-two:after {
  position: absolute;
  content: "";
  height: 20px;
  width: 100%;
  background: rgba(255, 255, 255, 0.3);
  top: -20px;
  left: 0;
}

.review-area-2 {
  margin-bottom: -250px;
}

.review-main {
  background: rgb(230, 247, 252);
  padding: 40px 40px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .review-main {
    padding: 25px 25px;
  }
}
@media (max-width: 575px) {
  .review-main {
    padding: 0px;
  }
}

.review-inner {
  display: grid;
  grid-template-columns: 48%;
  padding: 40px 50px;
  background: var(--clr-common-white);
  position: relative;
  row-gap: 30px;
  overflow: hidden;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .review-inner {
    padding: 35px 30px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .review-inner {
    padding: 30px 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .review-inner {
    grid-template-columns: 100%;
    padding: 35px 30px;
  }
}
@media (max-width: 575px) {
  .review-inner {
    background: rgb(230, 247, 252);
    padding: 30px 20px 0;
  }
}
.review-inner .round-1 {
  background: var(--clr-theme-primary);
  border-radius: 243px;
  opacity: 0.200000003;
  width: 150px;
  height: 150px;
  position: absolute;
  top: -70px;
  right: -60px;
}
.review-inner .round-2 {
  background: var(--clr-theme-primary);
  border-radius: 243px;
  opacity: 0.2;
  width: 45.47px;
  height: 41.22px;
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .review-inner .round-2 {
    display: none;
  }
}

.review-thumb-wrapper {
  position: absolute;
  width: 42%;
  right: 50px;
  bottom: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .review-thumb-wrapper {
    width: 50%;
    right: 0;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .review-thumb-wrapper {
    position: inherit;
    width: 100%;
    right: 0;
  }
}

.review-content-2 > p {
  font-size: 24px;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 35px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .review-content-2 > p {
    font-size: 20px;
  }
}
@media (max-width: 450px) {
  .review-content-2 > p {
    font-size: 18px;
  }
}

.review-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}
.review-navigation button {
  width: 50px;
  height: 50px;
  border: 1px solid var(--clr-border-2);
  border-radius: 50%;
}
.review-navigation button:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}

.review-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.testimonial-item.style-2 {
  background: rgb(237, 249, 253);
}

/*----------------------------------------*/
/*  BRAND CSS START
/*----------------------------------------*/
.brand-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  border-left: 1px solid var(--clr-border-2);
  align-items: center;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .brand-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .brand-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .brand-grid {
    grid-template-columns: 1fr;
  }
}

.brand-item {
  padding: 50px 0px;
  text-align: center;
  position: relative;
  display: inline-flex;
  align-items: center;
  height: 100%;
  justify-content: center;
  border: 1px solid var(--clr-border-2);
  border-left: 0;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .brand-item {
    padding: 60px 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .brand-item {
    padding: 50px 40px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .brand-item:nth-child(1) {
    border-bottom: 0;
  }
  .brand-item:nth-child(2) {
    border-bottom: 0;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .brand-item:nth-child(3) {
    border-bottom: 0;
  }
}
@media (max-width: 575px) {
  .brand-item {
    border-bottom: 0;
  }
  .brand-item:last-child {
    border: 1px solid var(--clr-border-2);
    border-left: 0;
  }
}
.brand-item:hover .brand-thumb img {
  opacity: 1;
}

.brand-thumb {
  max-width: 172px;
}
.brand-thumb img {
  width: 100%;
}

.brand-item-2 {
  text-align: center;
}

.brand-area-2 {
  padding: 35px 0;
}

.brand-active .swiper-wrapper {
  align-items: center;
}

/*----------------------------------------*/
/* 	BENEFIT CSS START
/*----------------------------------------*/
.benefit-item {
  padding: 0 25px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .benefit-item {
    padding: 0;
  }
}

.benefit-content-wrap {
  padding-right: 85px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .benefit-content-wrap {
    padding-right: 0;
  }
}
.benefit-content-wrap p {
  max-width: 500px;
}

.benefit-round {
  margin-bottom: 20px;
  background: var(--clr-theme-primary);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.benefit-round h3 {
  color: var(--clr-common-white);
}

.benefit-count {
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  background: var(--clr-theme-secondary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--clr-common-white);
}

.benefit-content h3 {
  font-size: 22px;
  margin-bottom: 15px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .benefit-content h3 {
    font-size: 20px;
  }
}
.benefit-content p {
  margin-bottom: 0;
}

/*----------------------------------------*/
/*  ACCORDION CSS STAR
/*----------------------------------------*/
.bd-faq.bd-faq-2column .accordion {
  display: flex;
  flex-wrap: wrap;
  column-gap: 50px;
  row-gap: 20px;
}
.bd-faq.bd-faq-2column .accordion > div {
  width: calc((100% - 50px) / 2);
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .bd-faq.bd-faq-2column .accordion > div {
    width: calc((100% - 0px) / 1);
  }
}

.bd-faq .accordion-button {
  border: 1px solid rgba(85, 85, 85, 0.2);
  border-radius: 30px;
  padding: 0 12px;
  height: 60px;
  font-size: 18px;
  font-weight: 500;
  padding-inline-end: 50px;
  text-align: left;
  background: var(--clr-common-white);
  gap: 15px;
  color: var(--clr-common-heading);
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  (max-width: 450px) {
  .bd-faq .accordion-button {
    padding: 0 18px;
  }
}
.bd-faq .accordion-button::after {
  content: "\f067";
  background-image: none;
  font-family: var(--bd-ff-fontawesome);
  font-size: 14px;
  font-weight: 400;
  height: 30px;
  width: 30px;
  border: 1px solid var(--clr-border-2);
  line-height: 30px;
  border-radius: 50%;
  margin-inline-start: 0;
  text-align: center;
  position: absolute;
  right: 19px;
  color: var(--clr-theme-primary);
}
.bd-faq .accordion-button:not(.collapsed) {
  background-color: transparent;
  box-shadow: none;
}
.bd-faq .accordion-button:not(.collapsed) .accordion-number {
  background: var(--clr-theme-primary);
  color: var(--clr-common-white);
}
.bd-faq .accordion-button:not(.collapsed)::after {
  background: var(--clr-common-white);
  color: var(--clr-theme-primary);
  content: "\f068";
}
.bd-faq .accordion-button:focus {
  box-shadow: none;
}
.bd-faq .accordion-body {
  font-size: 16px;
  line-height: 28px;
  padding: 20px 0px 5px 0px;
  color: var(--clr-text-body);
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  (max-width: 450px) {
  .bd-faq .accordion-body {
    font-size: 16px;
    padding: 24px 0px 5px 0px;
  }
}
.bd-faq .accordion-collapse {
  border: none;
  transition: all 0.5s;
}
.bd-faq .accordion-item {
  border: none;
}
[dir="rtl"] .bd-faq .accordion-item {
  background: none;
}
.bd-faq .accordion-item:first-of-type .accordion-button {
  border-radius: 30px;
}
.bd-faq .accordion-item:last-of-type .accordion-button.collapsed {
  border-width: 1px;
  border-radius: 30px;
}

.bd-faq-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-area.style-2 {
  background: var(--clr-theme-3);
}

.faq-thumb {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160px;
  height: 160px;
  background: var(--clr-common-white);
  text-align: center;
  line-height: 160px;
  border-radius: 550%;
}
@media (max-width: 450px) {
  .faq-thumb {
    transform: translate(-50%, -50%) scale(0.7);
  }
}

.accordion-number {
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  width: 36px;
  height: 36px;
  justify-content: center;
  border-radius: 50%;
  min-width: 36px;
  background: rgba(3, 175, 229, 0.2);
  color: var(--clr-theme-primary);
  font-weight: 500;
}

/*----------------------------------------*/
/* NEWSLETTER CSS START
/*----------------------------------------*/
.newsletter-title {
  font-size: 46px;
  color: var(--clr-common-white);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-title {
    font-size: 36px;
    color: var(--clr-common-white);
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-title {
    font-size: 40px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .newsletter-title {
    font-size: 28px;
    margin-bottom: 15px;
  }
}
@media (max-width: 575px) {
  .newsletter-title {
    font-size: 28px;
  }
}

.newsletter-grid {
  padding: 70px 50px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-grid {
    padding: 50px 50px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-grid {
    padding: 50px 30px;
  }
}
@media (max-width: 450px) {
  .newsletter-grid {
    padding: 35px 30px;
  }
}

.newsletter-input input {
  width: 100%;
  border-radius: 50px;
  padding-left: 25px;
  padding-right: 220px;
  border: 0;
}
@media (max-width: 450px) {
  .newsletter-input input {
    padding-right: 30px;
    height: 60px;
  }
}
.newsletter-input input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: var(--clr-text-4);
}
.newsletter-input input::-moz-placeholder {
  /* Firefox 19+ */
  color: var(--clr-text-4);
}
.newsletter-input input:-moz-placeholder {
  /* Firefox 4-18 */
  color: var(--clr-text-4);
}
.newsletter-input input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: var(--clr-text-4);
}
.newsletter-input input::placeholder {
  /* MODERN BROWSER */
  color: var(--clr-text-4);
}
.newsletter-input .fill-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 5px;
  height: 55px;
}
@media (max-width: 450px) {
  .newsletter-input .fill-btn {
    position: inherit;
    top: inherit;
    transform: none;
    right: 0px;
    width: 100%;
    margin-top: 20px;
  }
}

.newsletter-content {
  padding-right: 60px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-content {
    padding-right: 0;
  }
}

.newsletter-overlay {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 155px;
  z-index: -1;
}
.newsletter-overlay.is-gray {
  background: var(--clr-bg-3);
}

/*----------------------------------------*/
/*  COUNTER CSS START
/*----------------------------------------*/
.counter-wrapper {
  padding: 60px 0;
  border: 1px solid rgba(3, 175, 229, 0.2);
}

.counter-item {
  position: relative;
  text-align: center;
}
.counter-item:after {
  position: absolute;
  content: "";
  height: 60px;
  width: 1px;
  background: rgba(3, 175, 229, 0.2);
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .counter-item:after {
    display: none;
  }
}

div[class*="col"]:last-child .counter-item::after {
  display: none;
}

.counter-content h2 {
  color: var(--clr-theme-primary);
  margin-bottom: 10px;
  line-height: 1;
}
@media (max-width: 450px) {
  .counter-content h2 {
    font-size: 40px;
  }
}
.counter-content h5 {
  font-size: 18px;
}

.counter-content-2 > p {
  margin-bottom: 0;
  color: var(--clr-common-white);
}
.counter-content-2 h2,
.counter-content-2 span {
  font-size: 64px;
  font-weight: 500;
  color: var(--clr-common-white);
  display: inline-block;
  line-height: 1;
  margin-bottom: 10px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .counter-content-2 h2,
  .counter-content-2 span {
    font-size: 50px;
  }
}
@media (max-width: 450px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .counter-content-2 h2,
  .counter-content-2 span {
    font-size: 40px;
  }
}

.counter-item-2 {
  position: relative;
  text-align: center;
}
.counter-item-2:after {
  position: absolute;
  content: "";
  height: 90px;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .counter-item-2:after {
    display: none;
  }
}

div[class*="col"]:last-child .counter-item-2::after {
  display: none;
}

/*----------------------------------------*/
/*  ABOUT CSS START
/*----------------------------------------*/
.work-content {
  padding-right: 60px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .work-content {
    padding-right: 0;
  }
}

.work-line {
  position: absolute;
  z-index: -1;
  left: 50%;
  transform: translateX(-50%);
  top: 10px;
  width: 100%;
}
.work-line img {
  width: 100%;
}

.working-item {
  padding: 0px 40px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .working-item {
    padding: 0 10px;
  }
}

.working-icon span {
  height: 100px;
  width: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #d9f3fb;
  border-radius: 50%;
  margin-bottom: 25px;
}

.working-contentt h4 {
  margin-bottom: 15px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .working-contentt h4 {
    font-size: 20px;
  }
}
.working-contentt p {
  margin-bottom: 0;
}

.beforeAfter {
  z-index: 1 !important;
}
.beforeAfter img {
  max-width: inherit;
  object-fit: cover;
}

.beforeAfter div div div {
  background: transparent !important;
  height: 80px !important;
  width: 80px !important;
  line-height: 74px !important;
  border: 2px solid #ffffff !important;
  z-index: -1 !important;
}
.beforeAfter div div div i {
  border-color: #fff !important;
  padding: 5px !important;
  margin: 0 5px !important;
}

/*----------------------------------------*/
/*  DISCOUNT CSS START
/*----------------------------------------*/
.discount-item {
  background: rgba(3, 175, 229, 0.15);
  border-radius: 6px;
  min-height: 335px;
  position: relative;
  padding: 35px 25px;
}
@media (max-width: 450px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .discount-item {
    min-height: 300px;
    padding: 30px 25px;
  }
}
.discount-item:hover .discount-thumb {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.discount-content > span {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--clr-theme-secondary);
  display: block;
  margin-bottom: 5px;
}
.discount-content h3 {
  font-size: 22px;
  font-weight: 600;
}
.discount-content h3 a:hover {
  color: var(--clr-theme-secondary);
}

.discount-thumb {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  height: 205px;
  background-repeat: no-repeat;
  background-position: center;
}
.discount-thumb img {
  width: 100%;
}

.discount-slider-navigation button {
  height: 60px;
  width: 60px;
  background: var(--clr-common-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
  left: -30px;
}
@media (max-width: 575px) {
  .discount-slider-navigation button {
    left: 0;
  }
}
.discount-slider-navigation button:hover {
  background-color: var(--clr-theme-secondary);
  color: var(--clr-common-white);
}
.discount-slider-navigation .discount-slider-button-next {
  left: auto;
  right: -30px;
}
@media (max-width: 575px) {
  .discount-slider-navigation .discount-slider-button-next {
    right: 0;
  }
}

/*----------------------------------------*/
/*  PRODUCT CSS START
/*----------------------------------------*/
.product-modal .modal-dialog {
  max-width: 900px;
}

.modal-dialog.modal-dialog-centered {
  max-width: 1040px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .modal-dialog.modal-dialog-centered {
    margin: 15px;
  }
}

.product-modal-wrapper {
  padding: 40px 40px;
}
@media (max-width: 450px) {
  .product-modal-wrapper {
    padding: 30px 30px;
  }
}

.product-modal-close {
  color: var(--clr-common-white);
  display: block;
  line-height: 36px;
  text-align: center;
  background: var(--clr-theme-primary);
  width: 38px;
  height: 38px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -o-border-radius: 50px;
  -ms-border-radius: 50px;
  border-radius: 50px;
  outline: none;
  position: absolute;
  z-index: 5;
  right: -13px;
  top: -13px;
}

.product-item {
  border: 1px solid var(--clr-border-2);
  position: relative;
}
.product-item:hover .product-thumb img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

.product-badge {
  position: absolute;
  top: 25px;
  left: 20px;
}
.product-badge > span {
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  color: var(--clr-common-white);
  background-color: var(--clr-theme-secondary);
  border-radius: 30px;
  display: inline-block;
  padding: 6px 8px 4px;
  text-transform: capitalize;
}

.product-thumb {
  padding: 80px 60px 40px;
  overflow: hidden;
  max-width: 370px;
  margin: auto;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .product-thumb {
    padding: 80px 30px 40px;
  }
}
.product-thumb img {
  width: 100%;
}

.product-action-item {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
}

.product-title {
  font-size: 20px;
  margin-bottom: 5px;
}
@media (max-width: 450px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .product-title {
    font-size: 18px;
  }
}
.product-title a:hover {
  color: var(--clr-theme-secondary);
}

.product-content {
  padding: 25px 20px;
  border-top: 1px solid var(--clr-border-2);
  margin-top: 30px;
  position: relative;
}

.product-tag span {
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  color: var(--clr-theme-secondary);
  margin-bottom: 3px;
  display: block;
}

.product-old-price {
  font-size: 13px;
  color: var(--clr-text-3);
}

.product-new-price {
  font-size: 16px;
  color: var(--clr-theme-primary);
  font-weight: 500;
}

.tp-product-item-2:hover {
  visibility: visible;
}

.product-action-btn {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  font-size: 18px;
  color: var(--clr-common-black);
  background-color: var(--clr-theme-primary);
  border-bottom: 0;
  box-shadow: 0px 1px 2px rgba(1, 15, 28, 0.2);
  border-radius: 50%;
}
.product-action-btn:hover .product-tooltip {
  visibility: visible;
  opacity: 1;
  bottom: 125%;
  top: auto;
}

.product-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 500;
  font-size: 12px;
  color: var(--clr-common-white);
  background-color: var(--clr-common-black);
  display: inline-block;
  width: max-content;
  line-height: 1;
  padding: 4px 6px;
  border-radius: 4px;
  visibility: hidden;
  opacity: 0;
  z-index: 1;
}

.product-active .swiper-slide:last-child .product-item {
  border-right: 1px solid var(--clr-border-2);
}

.blog-item-4 {
  display: grid;
  align-items: center;
  background: var(--clr-common-white);
  padding: 30px;
  grid-template-columns: 45% 50%;
  gap: 30px 24px;
  justify-content: space-between;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .blog-item-4 {
    grid-template-columns: auto;
  }
}

.best-sale-item {
  border: 1px solid var(--clr-border-2);
}

.best-sale-thumb {
  padding: 0px 40px 20px;
}

.product-banner {
  min-height: 976px;
  width: 100%;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-banner {
    min-height: 550px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .product-banner {
    min-height: 500px;
  }
}

.product-banner-thumb {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 8px;
  background-position: bottom;
}

.blog-item-thumb-4 {
  height: 100%;
}

.product-banner-content {
  position: absolute;
  width: 100%;
  padding: 100px 60px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-banner-content {
    padding: 50px 50px;
  }
}

.shop-link-btn {
  display: inline-block;
  font-weight: 500;
  font-size: 16px;
  color: var(--clr-common-white);
  -webkit-transition: color 0.3s ease-in-out;
  -moz-transition: color 0.3s ease-in-out;
  -ms-transition: color 0.3s ease-in-out;
  -o-transition: color 0.3s ease-in-out;
  transition: color 0.3s ease-in-out;
  text-transform: uppercase;
}
.shop-link-btn:hover {
  color: var(--clr-common-white);
}
.shop-link-btn:hover svg {
  -webkit-transform: translate(3px);
  -moz-transform: translate(3px);
  -ms-transform: translate(3px);
  -o-transform: translate(3px);
  transform: translate(3px);
}

.shop-link-btn i,
.shop-link-btn svg {
  -webkit-transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  -moz-transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  -ms-transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  -o-transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  margin-left: 2px;
}

/*----------------------------------------*/
/*  VACCINE CSS START
/*----------------------------------------*/
.vaccine-content {
  padding-right: 150px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .vaccine-content {
    padding-right: 0;
  }
}
.vaccine-content > p {
  color: var(--clr-common-white);
  margin-bottom: 45px;
}

.vaccine-play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.vaccine-thumb {
  position: relative;
}
.vaccine-thumb img {
  width: 100%;
}
.vaccine-thumb:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 65%;
  background: linear-gradient(90deg, #03afe5 0%, rgba(3, 175, 229, 0) 100%);
  left: 0;
  top: 0;
}

.countdown-wrapper ul {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.countdown-wrapper ul li {
  display: inline-block;
  font-size: 14px;
  list-style-type: none;
  text-transform: uppercase;
  background: #4fc7ed;
  padding: 20px 10px 5px;
  text-align: center;
  color: var(--clr-common-white);
  position: relative;
  width: 85px;
}
.countdown-wrapper ul li span {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: var(--clr-common-white);
  margin-bottom: 15px;
  position: relative;
}
.countdown-wrapper ul li span::before {
  position: absolute;
  content: "";
  width: 85px;
  height: 1px;
  background: rgba(230, 247, 252, 0.2);
  bottom: -13px;
  left: -10px;
}

/*----------------------------------------*/
/*  SUPPORT CSS START
/*----------------------------------------*/
.support-item {
  padding: 40px 40px;
  background: rgb(217, 243, 251);
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .support-item {
    padding: 30px 30px;
  }
}
.support-item.is-light-yellow {
  background: rgb(249, 255, 224);
}
.support-item.is-pale-pink {
  background: rgb(255, 226, 229);
}

.support-content h3 {
  margin-bottom: 20px;
  font-size: 26px;
}
.support-content > p {
  margin-bottom: 30px;
}

.mobile__app-download {
  display: flex;
  align-items: center;
  gap: 20px;
}
.mobile__app-download .app__download {
  background: rgb(6, 6, 6);
  padding: 14px 20px;
  border: 0;
}
.mobile__app-download .app__download:hover {
  -webkit-transform: translateY(-3px);
  -moz-transform: translateY(-3px);
  -ms-transform: translateY(-3px);
  -o-transform: translateY(-3px);
  transform: translateY(-3px);
}

/*----------------------------------------*/
/* newsletter CSS START
/*----------------------------------------*/
.process-content {
  padding-right: 120px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .process-content {
    padding-right: 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .process-content {
    padding-right: 0px;
  }
}

.process-content > p {
  margin-bottom: 35px;
}

.process-info-number span {
  width: 57px;
  height: 57px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--clr-theme-secondary);
  border-radius: 50%;
  font-size: 26px;
  line-height: 1;
  color: var(--clr-common-white);
  font-weight: 500;
  position: relative;
}
.process-info-number span::before {
  position: absolute;
  content: "";
  height: calc(100% + 10px);
  width: calc(100% + 10px);
  background-color: rgba(235, 117, 59, 0.2);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.process-info-tem {
  display: flex;
  align-items: start;
  gap: 20px;
  position: relative;
}
.process-info-tem:not(:last-child) {
  margin-bottom: 40px;
}
.process-info-tem:not(:last-child)::before {
  position: absolute;
  content: "";
  height: 100px;
  width: 1px;
  border-left: 1px dashed rgb(235, 117, 59);
  left: 29px;
  z-index: -1;
  top: 20px;
}

.process-info-content h3 {
  font-size: 20px;
  margin-bottom: 5px;
}
@media (max-width: 450px) {
  .process-info-content h3 {
    font-size: 18px;
  }
}
.process-info-content > p {
  margin-bottom: 0;
}

.process-thumb-wrapper {
  margin: 0 15px;
}
.process-thumb-wrapper::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(22, 22, 22, 0.2);
  right: -15px;
  top: 20px;
  z-index: -1;
}

.process-thumb img {
  width: 100%;
}

/*----------------------------------------*/
/*  DOCTOR CSS START
/*----------------------------------------*/
.doctor-item {
  padding: 40px 40px;
  background: rgb(237, 249, 253);
}
@media (max-width: 450px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .doctor-item {
    padding: 30px 30px;
  }
}
.doctor-item:hover {
  background-color: var(--clr-theme-primary);
}
.doctor-item:hover .doctor-content h4 {
  color: var(--clr-common-white);
}
.doctor-item:hover .doctor-content span {
  color: var(--clr-common-white);
}
.doctor-item:hover .doctor-social a {
  color: rgba(255, 255, 255, 0.69);
}

.doctor-content {
  margin-bottom: 20px;
}
.doctor-content h4 {
  margin-bottom: 10px;
}
@media (max-width: 450px) {
  .doctor-content h4 {
    font-size: 22px;
  }
}
.doctor-content h4 a:hover {
  color: var(--clr-theme-secondary);
}
.doctor-content span {
  color: rgb(235, 117, 59);
}

.doctor-social {
  display: flex;
  justify-content: center;
  gap: 10px;
}
.doctor-social a {
  width: 45px;
  height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--clr-border-2);
  border-radius: 50%;
}
.doctor-social a:hover {
  background-color: var(--clr-theme-secondary);
  border-color: transparent;
}

.doctor-thumb {
  max-width: 220px;
  margin: 0 auto;
  margin-bottom: 25px;
}
.doctor-thumb img {
  width: 100%;
  border-radius: 50%;
  border: 5px solid var(--clr-common-white);
}

.doctor-card {
  position: absolute;
  bottom: 15px;
  width: calc(100% - 40px);
  background: var(--clr-common-white);
  left: 50%;
  transform: translateX(-50%);
  padding: 20px 20px;
}
.doctor-card .doctor-title {
  margin-bottom: 5px;
}
.doctor-card span {
  color: var(--clr-theme-secondary);
}

.doctor-item-2:hover .doctor-card {
  opacity: 0;
  visibility: hidden;
  bottom: -20px;
}
.doctor-item-2:hover .doctor-card-2 {
  bottom: 20px;
  opacity: 1;
  visibility: visible;
}

.doctor-card-2 {
  position: absolute;
  bottom: -50px;
  width: calc(100% - 40px);
  background: var(--clr-theme-primary);
  left: 50%;
  transform: translateX(-50%);
  padding: 25px 20px 25px;
  opacity: 0;
  visibility: hidden;
}
.doctor-card-2 .doctor-title {
  color: var(--clr-common-white);
  margin-bottom: 5px;
}
.doctor-card-2 .doctor-title a:hover {
  color: var(--clr-theme-secondary);
}
.doctor-card-2 span {
  color: var(--clr-common-white);
  margin-bottom: 15px;
  display: inline-block;
}
.doctor-card-2 .doctor-social a {
  color: var(--clr-common-white);
  border-color: var(--clr-common-white);
}
.doctor-card-2 .doctor-social a:hover {
  border-color: transparent;
}

.doctor-item-3:hover .doctor-social-3 {
  opacity: 1;
  visibility: visible;
  bottom: 0;
}
.doctor-item-3:hover .doctor-thumb-3 img {
  -webkit-transform: scale(1.05);
  -moz-transform: scale(1.05);
  -ms-transform: scale(1.05);
  -o-transform: scale(1.05);
  transform: scale(1.05);
}

.doctor-thumb-3 img {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.doctor-social-3 {
  background: var(--clr-theme-primary);
  display: inline-flex;
  padding: 15px 15px;
  gap: 10px;
  flex-direction: column;
  border-radius: 40px 40px 0px 40px;
  position: absolute;
  right: 0;
  bottom: -50px;
  opacity: 0;
  visibility: hidden;
}
.doctor-social-3 ul li {
  list-style: none;
}
.doctor-social-3 ul li:not(:last-child) {
  margin-bottom: 10px;
}
.doctor-social-3 ul li a {
  width: 40px;
  height: 40px;
  border: 1px solid var(--clr-common-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--clr-common-white);
}
.doctor-social-3 ul li a:hover {
  color: var(--clr-common-white);
  background-color: var(--clr-theme-secondary);
  border-color: transparent;
}

.doctor-skill {
  padding-right: 80px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .doctor-skill {
    padding-right: 0;
  }
}

.doctor-author-content {
  padding-left: 40px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .doctor-author-content {
    padding-left: 0;
  }
}

.doctor-author {
  display: grid;
  grid-template-columns: auto auto;
  border-top: 1px solid var(--clr-border-2);
  border-left: 1px solid var(--clr-border-2);
}
@media (max-width: 575px) {
  .doctor-author {
    grid-template-columns: auto;
  }
}

.doctor-author-info {
  display: flex;
  align-items: start;
  gap: 10px;
  padding: 25px 25px;
  border-bottom: 1px solid var(--clr-border-2);
  border-right: 1px solid var(--clr-border-2);
}
.doctor-author-info .content h5 {
  margin-bottom: 5px;
  font-size: 20px;
}
.doctor-author-info .content span {
  color: var(--clr-text-body);
}
.doctor-author-info .content span a:hover {
  color: var(--clr-theme-primary);
}

.author-social {
  position: absolute;
  bottom: 30px;
  padding: 25px 30px;
  background: var(--clr-theme-primary);
  border-radius: 150px;
  left: 50%;
  transform: translateX(-50%);
  display: inline-flex;
  gap: 7px;
}
.author-social a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--clr-common-white);
  border-radius: 50%;
  color: rgb(22, 22, 22);
}

/*----------------------------------------*/
/* WORK CHOOSE CSS START
/*----------------------------------------*/
.what-we-do-item {
  border: 1px solid var(--clr-border-2);
  padding: 40px 40px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .what-we-do-item {
    padding: 30px 25px;
  }
}

.what-we-do-icon {
  margin-bottom: 25px;
}
.what-we-do-icon span {
  width: 74px;
  height: 74px;
  background: rgba(3, 175, 229, 0.3);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.what-we-do-content h4 {
  margin-bottom: 20px;
}
@media (max-width: 450px) {
  .what-we-do-content h4 {
    margin-bottom: 15px;
    font-size: 20px;
  }
}
.what-we-do-content p {
  margin-bottom: 25px;
}

/*----------------------------------------*/
/*  ADVANTAGE CSS START
/*----------------------------------------*/
.advantage-item {
  padding: 0 55px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .advantage-item {
    padding: 0 30px;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .advantage-item {
    padding: 0;
  }
}

.advantage-icon {
  margin-bottom: 20px;
}
.advantage-icon > span {
  height: 100px;
  width: 100px;
  background: rgb(217, 243, 251);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
}

.advantage-count {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--clr-common-white);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: var(--clr-theme-secondary);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.advantage-content h4 {
  margin-bottom: 10px;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .advantage-content h4 {
    font-size: 20px;
  }
}
.advantage-content P {
  margin-bottom: 0;
}

/*----------------------------------------*/
/*  FAQ CSS START
/*----------------------------------------*/
.faq__content {
  padding-right: 50px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .faq__content {
    padding-right: 0;
  }
}

.bd__faq .accordion-button {
  font-size: 24px;
  padding: 30px 30px 30px 30px;
  font-weight: 500;
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .bd__faq .accordion-button {
    font-size: 20px;
  }
}
.bd__faq .accordion-button:focus {
  box-shadow: none;
}
.bd__faq .accordion-button:not(.collapsed) {
  background: rgba(3, 175, 229, 0.1);
  box-shadow: none;
}
.bd__faq .accordion-button:not(.collapsed)::after {
  border-color: transparent;
  background: var(--clr-theme-primary);
  color: var(--clr-common-white);
}
.bd__faq .accordion-button::after {
  position: absolute;
  right: 30px;
  content: "\f107";
  background-image: none;
  font-family: var(--bd-ff-fontawesome);
  font-size: 18px;
  font-weight: 400;
  height: 46px;
  width: 46px;
  border: 1px solid var(--clr-border-primary);
  line-height: 46px;
  border-radius: 50%;
  text-align: center;
  color: var(--clr-text-primary);
}
@media (max-width: 450px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .bd__faq .accordion-button::after {
    left: 20px;
  }
}
.bd__faq .accordion-body {
  padding-left: 30px;
  padding-top: 0;
  padding-bottom: 35px;
  padding-right: 30px;
  background: rgba(3, 175, 229, 0.1);
  border-radius: 0;
}
.bd__faq .accordion-body > P {
  margin-bottom: 0;
}
.bd__faq .accordion-item {
  background: transparent;
  border: 1px solid var(--clr-border-2);
  border-radius: 0;
}
.bd__faq .accordion-item:not(:last-child) {
  margin-bottom: 20px;
}

/*----------------------------------------*/
/*  PRODUCT DETAILS CSS START
/*----------------------------------------*/
.bd-product {
  position: absolute;
  right: 0;
  height: 100%;
  width: 29%;
  top: 0;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .bd-product {
    width: 0;
  }
}
.bd-product img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.bd-product__bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 26%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .bd-product__bg {
    width: 30%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .bd-product__bg {
    width: 0%;
  }
}
.bd-product__bg::before {
  position: absolute;
  content: "";
  height: 100%;
  right: 0;
  top: 0;
  width: 100%;
  background-color: var(--clr-common-body-text);
  opacity: 1;
  mix-blend-mode: multiply;
}
.bd-product__item {
  -webkit-box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
  -moz-box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
  padding: 10;
  position: relative;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background-color: var(--clr-common-white);
}
.bd-product__item:hover .bd-product__action a {
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
}
.bd-product__action {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 10px;
}
.bd-product__action a {
  height: 50px;
  width: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: space-evenly;
  background-color: var(--clr-common-white);
  -webkit-box-shadow: 0px 10px 20px 0px rgba(8, 0, 42, 0.08);
  -moz-box-shadow: 0px 10px 20px 0px rgba(8, 0, 42, 0.08);
  box-shadow: 0px 10px 20px 0px rgba(8, 0, 42, 0.08);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
}
.bd-product__action a:hover {
  color: var(--clr-common-white);
  background: var(--clr-theme-primary);
}
.bd-product__img img {
  padding: 50px 35px 10px;
  width: 100%;
}
@media (max-width: 575px) {
  .bd-product__img img {
    padding: 0px 70px 0px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .bd-product__img img {
    padding: 50px 70px 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .bd-product__img img {
    padding: 0px 35px 0px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .bd-product__img img {
    padding: 30px 35px 0px;
  }
}
.bd-product__content {
  padding: 23px 0;
}
.bd-product__content h4 {
  font-size: 20px;
  margin-bottom: 5px;
  font-weight: var(--bd-fw-bold);
}
.bd-product__content h4 a:hover {
  color: var(--clr-theme-primary);
}
.bd-product__new-price {
  font-size: 16px;
  color: var(--clr-theme-primary);
  font-family: var(--bd-ff-heading);
  font-weight: var(--bd-fw-bold);
}
.bd-product__old-price {
  font-size: 16px;
  color: var(--clr-theme-8);
  font-family: var(--bd-ff-heading);
  font-weight: var(--bd-fw-bold);
  margin-left: 5px;
}

.bd-product__result h4 {
  font-size: 18px;
}
.bd-product__filter-btn {
  margin-right: 25px;
  padding-right: 25px;
  position: relative;
}
.bd-product__filter-btn button {
  font-weight: var(--bd-fw-bold);
  font-size: 14px;
  text-transform: uppercase;
}
.bd-product__filter-btn:before {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 2px;
  height: 15px;
  background-color: #dcdcdc;
}

.btn-dropdown__options select {
  font-size: 14px;
  padding: 3px 5px;
}

.bd-product__filter-style .nav-link {
  font-size: 20px;
  margin-left: 15px;
  color: var(--clr-common-body-text);
  border: 0;
  padding: 0;
}
.bd-product__filter-style .nav-link.active {
  border: 0;
  color: var(--clr-theme-primary);
}
.bd-product__filter-style.nav-tabs {
  border: 0;
  margin-left: 15px;
}

.product__content-list ul {
  padding: 13px 0px 20px 0px;
}
.product__content-list ul li {
  list-style: none;
  position: relative;
  padding-left: 15px;
}
.product__content-list ul li::after {
  position: absolute;
  content: "";
  left: 0;
  top: 12px;
  width: 6px;
  height: 6px;
  background-color: var(--clr-common-body-text);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

.product__add-cart a {
  padding: 16px 33px;
  text-transform: uppercase;
}
.product__add-cart a i {
  margin-left: 0;
  margin-right: 5px;
}
.product__add-wish-btn {
  display: inline-block;
  height: 60px;
  width: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 20px;
  color: #b0b0b0;
  border: 1px solid var(--clr-border-2);
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
}
.product__add-wish-btn:hover {
  color: var(--clr-common-white);
  border-color: var(--clr-theme-secondary);
  background-color: var(--clr-theme-secondary);
}
.product__details-action {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 15px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .product__details-content {
    padding-right: 0;
  }
}
.product__details-content > p {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 30px;
  color: var(--clr-text-2);
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .product__details-thumb-wrapper {
    margin-right: 0;
    margin-bottom: 40px;
  }
}
.product__details-thumb-tab .nav-tabs {
  padding: 0;
  border: 0;
  margin: 0;
}
.product__details-thumb-tab .nav-tabs .nav-link {
  padding: 0;
  border: 1px solid var(--clr-border-2);
  margin: 0;
  margin-bottom: 20px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  overflow: hidden;
}
@media (max-width: 575px) {
  .product__details-thumb-tab .nav-tabs .nav-link {
    margin-right: 20px;
  }
}
.product__details-thumb-tab .nav-tabs .nav-link.active,
.product__details-thumb-tab .nav-tabs .nav-link:hover {
  border: 1px solid var(--clr-theme-primary);
}
.product__details-thumb-tab .nav-tabs .nav-link img {
  width: 70px;
  height: 80px;
  min-width: 70px;
  font-size: 18px;
}
.product__details-thumb-big img {
  width: 100%;
}
.product__details-tag a {
  display: inline-block;
  font-weight: var(--bd-fw-sbold);
  text-align: center;
  background-color: #eb753b;
  padding: 0px 8px;
  text-transform: uppercase;
  font-size: 14px;
  border-radius: 4px;
  color: var(--clr-common-white);
}
.product__details-rating a {
  display: inline-block;
  color: var(--clr-theme-secondary);
  font-size: 16px;
}
.product__details-title {
  font-size: 26px;
  margin-bottom: 25px;
}
@media (max-width: 575px) {
  .product__details-title {
    font-size: 20px;
  }
}
.product__details-title a:hover {
  color: var(--clr-theme-primary);
}
.product__details-price {
  margin-bottom: 20px;
}
.product__details-price .price {
  font-size: 26px;
}
.product__details-price .old-price {
  font-size: 20px;
  font-weight: 500;
  color: #cfcfcf;
  text-decoration: line-through;
}
.product__details-price .new-price {
  font-size: 32px;
  color: var(--clr-theme-primary);
  font-weight: var(--bd-fw-bold);
  margin-left: 5px;
}
@media (max-width: 575px) {
  .product__details-price .new-price {
    font-size: 30px;
  }
}
.product__details-meta span {
  font-size: 16px;
  font-weight: 700;
}
.product__details-meta a {
  font-size: 16px;
  color: var(--clr-text-2);
}
.product__details-meta a:hover {
  color: var(--clr-theme-primary);
}
.product__details-share span {
  font-size: 16px;
  font-weight: 700;
  margin-right: 10px;
}
.product__details-share a {
  font-size: 16px;
  color: #999999;
  margin-right: 15px;
}
.product__details-share a:hover {
  color: var(--clr-theme-primary);
}
.product__details-des p {
  font-size: 16px;
  color: var(--clr-text-2);
}
.product__details-des p:last-child {
  margin-bottom: 0;
}
.product__details-des p span {
  font-weight: 600;
}
.product__details-info ul li {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  padding: 15px 30px;
}
@media (max-width: 575px) {
  .product__details-info ul li {
    flex-wrap: wrap;
    gap: 15px 40px;
    padding: 30px 20px;
  }
}
.product__details-info ul li:nth-child(2n + 1) {
  background: var(--clr-bg-5);
}
.product__details-info ul li h4 {
  font-size: 16px;
  color: var(--clr-common-body-text);
  margin-bottom: 0;
  display: inline-block;
  width: 25%;
}
.product__details-info ul li span {
  font-size: 16px;
  color: var(--clr-common-body-text);
}
.product__details-review.product-details-review-padding {
  padding: 50px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product__details-review.product-details-review-padding {
    padding: 15px;
  }
}
@media (max-width: 575px) {
  .product__details-review.product-details-review-padding {
    padding: 10px;
  }
}
.product__details-review .comments-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 35px;
}
@media (max-width: 575px) {
  .product__details-review .comments-title {
    font-size: 20px;
  }
}
.product__details-review .comments-box {
  padding: 30px 0;
  padding-bottom: 18px;
}
.product__details-review .latest-comments ul li:last-child .comments-box {
  padding-bottom: 0;
}
.product__details-review .latest-comments ul li {
  list-style: none;
}
.product__details-review .latest-comments li:first-child .comments-box {
  border-top: 0;
  padding-top: 0;
}
.product__details-review .comments-avatar img {
  max-width: 60px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
.product__details-review .comments-text {
  overflow: hidden;
  padding-top: 4px;
}
.product__details-review .comments-text p {
  font-size: 16px;
  color: var(--clr-text-2);
}
.product__details-review .avatar-name {
  margin-bottom: 3px;
  overflow: hidden;
  position: relative;
  text-transform: uppercase;
}
.product__details-review .avatar-name h5 {
  font-size: 16px;
  margin-bottom: 0px;
  color: var(--clr-common-black);
  font-weight: 600;
}
.product__details-review .latest-comments li.children {
  margin-left: 100px;
}
@media (max-width: 575px) {
  .product__details-review .latest-comments li.children {
    margin-left: 15px;
  }
}
.product__details-review .comments-date span {
  font-size: 13px;
  color: var(--clr-text-2);
}
.product__details-review .user-rating ul li {
  display: inline-block;
}
.product__details-review .user-rating ul li a {
  font-size: 14px;
  color: var(--clr-theme-secondary);
}
.product__details-review-count a {
  color: var(--clr-text-2);
}
.product__details-comment .comment-input input,
.product__details-comment .comment-input textarea {
  width: 100%;
  height: 60px;
  border: none;
  outline: none;
  border: 1px solid var(--clr-border-2);
  font-size: 15px;
  color: var(--clr-common-black);
  line-height: 48px;
  margin-bottom: 15px;
  padding: 15px;
}
.product__details-comment .comment-input input::placeholder,
.product__details-comment .comment-input textarea::placeholder {
  color: var(--clr-common-black);
  opacity: 0.4;
}
.product__details-comment .comment-input input:focus,
.product__details-comment .comment-input textarea:focus {
  outline: none;
  border-color: var(--clr-theme-primary);
}
.product__details-comment .comment-input textarea {
  height: 170px;
  resize: none;
  line-height: 1;
}
.product__details-comment .comment-agree input {
  margin: 0;
  appearance: none;
  -moz-appearance: none;
  display: inline-block;
  width: 18px;
  height: 18px;
  background: transparent;
  border: 1px solid var(--clr-theme-primary);
  outline: none;
  flex: 0 0 auto;
}
.product__details-comment .comment-agree input:checked {
  position: relative;
  border-color: var(--clr-common-black);
}
.product__details-comment .comment-agree input:checked::after {
  box-sizing: border-box;
  content: "\f00c";
  position: absolute;
  font-family: var(--bd-ff-fontawesome);
  font-size: 11px;
  color: var(--clr-common-black);
  top: 45%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.product__details-comment .comment-agree input:checked ~ label {
  color: var(--clr-common-black);
}
.product__details-comment .comment-agree input:hover {
  cursor: pointer;
}
.product__details-comment .comment-agree label {
  margin-left: 5px;
  font-size: 15px;
}
.product__details-comment .comment-agree label:hover {
  cursor: pointer;
}
.product__details-comment .comment-title h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  text-transform: uppercase;
}
.product__details-comment .comment-title p {
  color: var(--clr-text-2);
}
.product__details-comment .comment-rating span {
  font-size: 15px;
  color: var(--clr-text-2);
  margin-right: 5px;
}
.product__details-comment .comment-rating ul {
  display: inline-block;
}
.product__details-comment .comment-rating ul li {
  display: inline-block;
}
.product__details-comment .comment-rating ul li a {
  font-size: 14px;
  color: var(--clr-theme-secondary);
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .product__details-more-tab-content {
    margin-top: 50px;
  }
}
.product__details-more-tab .nav-tabs {
  margin: 0;
  padding: 0;
  border: 0;
}
.product__details-more-tab .nav-link {
  margin-bottom: 15px;
  padding: 15px 25px;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  border: 1px solid var(--clr-border-2);
  text-align: left;
  color: var(--clr-common-body-text);
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}
.product__details-more-tab .nav-link.active,
.product__details-more-tab .nav-link:hover {
  border-color: var(--clr-theme-primary);
  color: var(--clr-common-white);
  background-color: var(--clr-theme-primary);
}
.product__quantity .product-quantity-wrapper {
  position: relative;
  width: 120px;
  height: 60px;
  line-height: 60px;
  border: 1px solid var(--clr-border-2);
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
}
.product__quantity .product-quantity-wrapper .cart-plus,
.product__quantity .product-quantity-wrapper .cart-minus {
  position: absolute;
  top: 50%;
  left: 10px;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  font-size: 16px;
  background: transparent;
  color: #9e9e9e;
  border: 0;
}
.product__quantity .product-quantity-wrapper .cart-plus:hover,
.product__quantity .product-quantity-wrapper .cart-minus:hover {
  cursor: pointer;
  color: var(--clr-theme-primary);
  background: transparent;
}
.product__quantity .product-quantity-wrapper .cart-plus {
  left: auto;
  right: 10px;
}
.product__quantity .product-quantity-wrapper .cart-input {
  height: 60px;
  width: 120px;
  text-align: center;
  font-size: 16px;
  border: none;
  display: inline-block;
  vertical-align: middle;
  margin: 0 -3px;
  padding-bottom: 4px;
  background: transparent;
  color: var(--clr-theme-primary);
  font-weight: var(--bd-fw-medium);
}
.product__quantity .product-quantity-wrapper .cart-input:focus {
  outline: none;
}
.product__add-review span a {
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}
.product__add-review span a::after {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 1px;
  height: 80%;
  background: var(--tp-border-1);
}
.product__add-review span a:hover {
  color: var(--clr-theme-primary);
}
.product__add-review span:last-child a::after {
  display: none;
}

.categories {
  padding: 5px 0;
}

.product-modal .modal-dialog {
  max-width: 950px;
}

.product-modal-links {
  padding-top: 15px;
  padding-bottom: 15px;
  border-top: 1px solid var(--clr-border-2);
  border-bottom: 1px solid var(--clr-border-2);
}

.product__safe-checkout a {
  display: inline-block;
  margin-right: 10px;
}
.product__safe-checkout a:last-child {
  margin-right: 0;
}
.product__safe-checkout h5 {
  margin-bottom: 15px;
}

.product__review {
  display: flex;
  align-items: center;
  padding-top: 15px;
  padding-bottom: 15px;
  border-top: 1px solid var(--clr-border-2);
  border-bottom: 1px solid var(--clr-border-2);
  margin-bottom: 20px;
  gap: 25px;
  font-size: 16px;
  flex-wrap: wrap;
}

.product-modal-img {
  background-color: var(--clr-bg-5);
}

.product-modal-content {
  padding-left: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal-content {
    padding-left: 0;
  }
}

.product-modal-title {
  font-size: 24px;
  margin-bottom: 20px;
}
@media (max-width: 575px) {
  .product-modal-title {
    font-size: 20px;
  }
}

.product-modal-content .product__details-price {
  margin-bottom: 20px;
}

.product__details-thumb-big img {
  background: var(--clr-bg-5);
}

@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .product-modal-box {
    margin-bottom: 15px;
  }
}

.cart-area .table > :not(:first-child) {
  border-top: 0;
}
.cart-area .table-content table {
  background: var(--clr-common-white);
  border-color: var(--clr-border-2);
  border-radius: 0;
  border-style: solid;
  border-width: 1px 0 0 1px;
  text-align: center;
  width: 100%;
  margin-bottom: 0;
}
.cart-area .table-content table a {
  white-space: nowrap;
}
.cart-area .table-content .product-quantity {
  float: none;
}
.cart-area .table-content table td.product-name {
  font-size: 18px;
  text-transform: capitalize;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .cart-area .table-content table td.product-name {
    font-size: 14px;
  }
}
.cart-area .table > :not(:last-child) > :last-child > * {
  border-bottom-color: var(--clr-border-2);
}
.cart-area .table-content table td {
  border-top: medium none;
  padding: 20px 10px;
  vertical-align: middle;
  font-size: 16px;
}
.cart-area .table-content table td.product-name a:hover {
  color: var(--clr-common-heading);
}
.cart-area .table-content table td.product-subtotal {
  font-size: 16px;
}
.cart-area .table-content table td .cart-plus-minus {
  float: none;
  margin: 0 auto;
}
.cart-area .table-content table th,
.cart-area .table-content table td {
  border-bottom: 1px solid var(--clr-border-2);
  border-right: 1px solid var(--clr-border-2);
  color: var(--clr-common-heading);
}
.cart-area .table td,
.cart-area .table th {
  border-top: 1px solid var(--clr-border-2);
}
.cart-area .coupon-all {
  margin-top: 50px;
}
.cart-area .coupon {
  float: left;
  gap: 15px;
  flex-wrap: wrap;
}
@media (max-width: 767px) {
  .cart-area .coupon {
    float: none;
  }
}
.cart-area .coupon2 {
  float: right;
}
@media (max-width: 767px) {
  .cart-area .coupon2 {
    float: none;
    margin-top: 15px;
  }
}
.cart-area .cart-page-total {
  padding-top: 50px;
}
.cart-area .cart-page-total > h2 {
  font-size: 20px;
  margin-bottom: 20px;
  text-transform: capitalize;
}
.cart-area .cart-page-total > ul {
  border: 1px solid var(--clr-border-2);
}
.cart-area .cart-page-total > ul > li {
  list-style: none;
  font-size: 15px;
  color: #575757;
  padding: 14px 30px;
  border-bottom: 1px solid var(--clr-border-2);
  font-weight: 400;
}
.cart-area .cart-page-total ul > li > span {
  float: right;
}
.cart-area .cart-page-total li:last-child {
  border-bottom: 0;
}
.cart-area td.product-thumbnail img {
  width: 125px;
}
.cart-area .product-quantity-form {
  margin: auto;
  width: 122px;
  height: 58px;
  border: 1px solid var(--clr-border-2);
  text-align: center;
  position: relative;
}
.cart-area .product-quantity-form:hover {
  border-color: var(--clr-theme-primary);
}
.cart-area .cart-plus,
.cart-area .cart-minus {
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 45px;
  height: 40px;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  font-size: 14px;
  background: transparent;
  border: none;
  outline: none;
}
.cart-area .cart-plus:hover,
.cart-area .cart-minus:hover {
  cursor: pointer;
  color: var(--clr-theme-primary);
  background: transparent;
}
.cart-area .cart-plus {
  left: auto;
  right: 0;
}
.cart-area .cart-input {
  height: 58px;
  width: 35px;
  text-align: center;
  font-size: 14px;
  border: none;
  display: inline-flex;
  vertical-align: middle;
  background: transparent;
  padding: 0;
  align-items: center;
  justify-content: center;
}
.cart-area .cart-input:focus {
  outline: none;
}

.coupon-accordion h3 {
  background-color: #f6f6f6;
  border-top: 3px solid var(--clr-common-heading);
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 25px;
  padding: 1em 2em 1em 3.5em;
  position: relative;
  width: auto;
}
.coupon-accordion h3::before {
  content: "\f07b";
  left: 15px;
  top: 13px;
  position: absolute;
  color: #575757;
  font-family: "Font Awesome 5 Pro";
  font-weight: 700;
}
.coupon-accordion span {
  color: #575757;
  cursor: pointer;
  transition: 0.3s;
  font-weight: 500;
}

.coupon-content {
  border: 1px solid var(--clr-border-2);
  display: none;
  margin-bottom: 20px;
  padding: 30px;
}

.coupon-info p {
  margin-bottom: 0;
}
.coupon-info p.coupon-text {
  margin-bottom: 15px;
}

.coupon-accordion span:hover,
p.lost-password a:hover {
  color: var(--clr-theme-primary);
}

.coupon-info p.form-row-first label,
.coupon-info p.form-row-last label {
  display: block;
  color: var(--clr-theme-primary);
}

.coupon-info p.form-row-first label span.required,
.coupon-info p.form-row-last label span.required {
  color: var(--clr-theme-primary);
  font-weight: 700;
}

.coupon-info p.form-row-first input,
.coupon-info p.form-row-last input {
  border: 1px solid var(--clr-border-2);
  height: 60px;
  line-height: 60px;
  margin: 0 0 14px;
  max-width: 100%;
  padding: 0 0 0 10px;
  width: 100%;
  outline: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.coupon-info p.form-row-first input:focus,
.coupon-info p.form-row-last input:focus {
  border-color: var(--clr-common-heading);
}

.coupon-info p.form-row input[type="submit"]:hover,
p.checkout-coupon input[type="submit"]:hover {
  background: #3e976c none repeat scroll 0 0;
}

.coupon-info p.form-row input[type="checkbox"] {
  position: relative;
  top: 2px;
}

.form-row > label {
  margin-top: 15px;
  margin-left: 15px;
  color: #575757;
  display: flex;
  gap: 10px;
}

.buttons-cart input,
.coupon input[type="submit"],
.buttons-cart a,
.coupon-info p.form-row input[type="submit"] {
  background: #252525 none repeat scroll 0 0;
  border: medium none;
  color: var(--clr-common-white);
  display: inline-block;
  float: left;
  font-size: 12px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  margin-right: 15px;
  padding: 0 15px;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
}

p.lost-password {
  margin-top: 15px;
}
p.lost-password a {
  color: #6f6f6f;
}

p.checkout-coupon input[type="text"] {
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
  width: 100%;
  border: 1px solid var(--clr-border-2);
  margin-bottom: 15px;
  outline: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
p.checkout-coupon input[type="text"]:focus {
  border-color: var(--clr-border-2);
}

.coupon-checkout-content {
  display: none;
}

.checkbox-form .ship-different-title {
  border-bottom: 1px solid var(--clr-border-2);
  margin: 0 0 20px;
  padding-bottom: 10px;
  width: 100%;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}

.country-select {
  margin-bottom: 30px;
  position: relative;
}
.country-select::before {
  position: absolute;
  top: 55px;
  content: "";
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 8px solid var(--clr-theme-primary);
  right: 15px;
}
.country-select select {
  width: 100%;
  background-color: transparent;
  border: 1px solid var(--clr-border-2);
  padding: 0 15px;
  height: 60px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.country-select .nice-select {
  border: 1px solid var(--clr-border-2);
  height: 60px;
  line-height: 60px;
  padding-left: 20px;
  width: 100%;
  color: #575757;
  margin-bottom: 20px;
}
.country-select .nice-select::after {
  border-bottom: 1px solid var(--clr-common-heading);
  border-right: 1px solid var(--clr-common-heading);
  height: 7px;
  width: 7px;
}
.country-select .nice-select .list {
  width: 100%;
}

.country-select label,
.checkout-form-list label {
  color: var(--clr-common-heading);
  display: block;
  margin: 0 0 5px;
}

.country-select label span.required,
.checkout-form-list label span.required {
  color: var(--clr-common-heading);
}

.checkout-form-list label {
  color: var(--clr-common-heading);
}
.checkout-form-list input[type="checkbox"] {
  display: inline-block;
  margin-right: 10px;
  position: relative;
  top: 1px;
}

.checkout-form-list input[type="text"],
.checkout-form-list input[type="password"],
.checkout-form-list input[type="email"] {
  background: var(--clr-common-white);
  border: 1px solid var(--clr-border-2);
  border-radius: 0;
  height: 60px;
  line-height: 60px;
  padding: 0 0 0 20px;
  width: 100%;
  outline: none;
}

.checkout-form-list input[type="text"]::-moz-placeholder,
.checkout-form-list input[type="password"]::-moz-placeholder,
.checkout-form-list input[type="email"]::-moz-placeholder {
  color: #575757;
  opacity: 1;
}

.checkout-form-list input[type="text"]::placeholder,
.checkout-form-list input[type="password"]::placeholder,
.checkout-form-list input[type="email"]::placeholder {
  color: #575757;
  opacity: 1;
}

.create-acc label {
  color: #575757;
  display: inline-block;
}

.create-account {
  display: none;
}

.ship-different-title label {
  display: inline-block;
  margin-right: 10px;
}

.order-notes textarea {
  border: 1px solid var(--clr-border-2);
  height: 90px;
  padding: 15px;
  width: 100%;
  resize: none;
  padding-left: 20px;
  outline: none;
}
.order-notes textarea::-moz-placeholder {
  color: #575757;
  opacity: 1;
}
.order-notes textarea::placeholder {
  color: #575757;
  opacity: 1;
}

#ship-box-info {
  display: none;
}

.panel-group .panel {
  border-radius: 0;
}

.panel-default > .panel-heading {
  border-radius: 0;
}

.your-order {
  padding: 30px 40px 45px;
  border: 1px solid var(--clr-border-2);
}

@media (max-width: 767px) {
  .your-order {
    padding: 15px;
  }
}
.your-order h3 {
  border-bottom: 1px solid var(--clr-border-2);
  font-size: 24px;
  margin: 0 0 20px;
  padding-bottom: 10px;
  width: 100%;
}

.your-order-table table {
  background: none;
  border: 0;
  width: 100%;
}

.your-order-table table th,
.your-order-table table td {
  border-bottom: 1px solid var(--clr-border-2);
  border-right: medium none;
  color: #575757;
  font-size: 16px;
  padding: 15px 0;
  text-align: left;
}
@media (max-width: 575px) {
  .your-order-table table th,
  .your-order-table table td {
    padding-right: 10px;
  }
}

.your-order-table table th {
  border-top: medium none;
  color: var(--clr-common-heading);
  font-weight: normal;
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
  width: 250px;
}

.panel-body > p {
  color: #222;
}

.your-order-table table .shipping ul li input {
  position: relative;
  top: 2px;
}
.your-order-table table .shipping ul li label {
  color: #575757;
}
.your-order-table table .shipping th {
  vertical-align: top;
}
.your-order-table table .order-total th {
  border-bottom: 0;
}
.your-order-table table .order-total td {
  border-bottom: medium none;
}
.your-order-table table tr.cart_item:hover {
  background: #f9f9f9;
}
.your-order-table table tr.order-total td span {
  color: var(--clr-theme-primary);
  font-size: 18px;
  font-weight: 500;
}

.shipping ul li {
  list-style: none;
}

.payment-method {
  margin-top: 40px;
}
.payment-method .accordion-item:last-of-type {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.payment-method .accordion-item {
  background-color: var(--clr-common-white);
  border: 0;
  border-bottom: 1px solid var(--clr-border-2);
}
.payment-method .accordion-button {
  font-size: 16px;
  font-weight: 500;
  color: var(--clr-common-heading);
  padding: 23px 0;
  border: none;
}
.payment-method .accordion-button:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.payment-method .accordion-button::after {
  position: absolute;
  content: "\f067";
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  font-family: var(--bd-ff-fontawesome);
  font-size: 16px;
  font-weight: 400;
  margin-left: 0;
  background-image: none;
}
.payment-method .accordion-button:not(.collapsed) {
  color: var(--clr-common-heading);
  background-color: var(--clr-common-white);
  box-shadow: none;
}
.payment-method .accordion-button:not(.collapsed)::after {
  content: "\f068";
}
.payment-method .accordion-body {
  padding: 8px 0;
  padding-bottom: 40px;
}
.payment-method .accordion-collapse {
  border: none;
}

.panel-title > a {
  display: block;
}

.order-button-payment input {
  background: #232323 none repeat scroll 0 0;
  border: medium none;
  color: var(--clr-common-white);
  font-size: 15px;
  font-weight: 600;
  height: 40px;
  margin: 0px 0 0;
  padding: 0;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
  width: 100%;
}
.order-button-payment input:hover {
  background: #3e976c none repeat scroll 0 0;
}
.order-button-payment button {
  width: 100%;
}

.payment-method .btn-link {
  user-select: none;
  -moz-user-select: none;
  background: no-repeat;
  border: medium none;
  border-radius: 0;
  color: #444;
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 1px;
  line-height: 1;
  margin-bottom: 0;
  padding: 3px 10px;
  text-align: center;
  text-transform: uppercase;
  transition: all 0.3s ease 0s;
  vertical-align: middle;
  white-space: nowrap;
  text-decoration: none;
}
.payment-method .card {
  background-color: var(--clr-common-white);
  border: 1px solid var(--clr-border-2);
  border-radius: 0;
  margin-bottom: 10px;
}
.payment-method .accordion .card:first-of-type {
  border: 1px solid var(--clr-border-2);
}
.payment-method .card-header {
  background-color: var(--clr-common-white);
  border-bottom: 1px solid var(--clr-border-2);
}

.card-header:first-child {
  border-radius: 0;
}

.product-details-img {
  margin-right: 50px;
}
@media (max-width: 575px) {
  .product-details-img {
    margin-right: 0;
  }
}
.product-details-img img {
  width: 100%;
}

.product-side-info .product-name {
  font-size: 38px;
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .product-side-info .product-name {
    font-size: 28px;
  }
}

.product-side-info .product-price {
  font-size: 22px;
  font-weight: 600;
  color: var(--clr-theme-primary);
}
@media only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .product-side-info .product-price {
    font-size: 18px;
  }
}

.product-side-info p {
  margin-top: 30px;
  margin-bottom: 40px;
}

.product-quantity-cart {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}
.product-quantity-cart .cart-minus {
  height: 59px;
  width: 55px;
  border: none;
}
.product-quantity-cart .cart-input {
  height: 55px;
  width: 55px;
  border: none;
  text-align: center;
}
.product-quantity-cart .product-quantity-form {
  border: 1px solid var(--clr-border-2);
  border-radius: 4px;
}

.product-quantity-form .cart-plus {
  height: 59px;
  width: 55px;
  border: none;
}

/*----------------------------------------*/
/*  ERROR CSS START
/*----------------------------------------*/
.error-content {
  max-width: 680px;
  margin: 0 auto;
}
.error-content h2 {
  margin-bottom: 30px;
}

/*----------------------------------------*/
/*  CONTACT CSS START
/*----------------------------------------*/
.contact-info-item {
  padding: 40px 40px;
  background: rgba(3, 175, 229, 0.07);
}
@media (max-width: 450px) {
  .contact-info-item {
    padding: 30px 30px;
  }
}

.contact-info-icon {
  margin-bottom: 20px;
}
.contact-info-icon span {
  height: 80px;
  width: 80px;
  background: var(--clr-theme-primary);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: var(--clr-common-white);
  border-radius: 50%;
}

.contact-info-content p {
  margin-bottom: 0;
}
.contact-info-content p a:hover {
  color: var(--clr-theme-secondary);
}
.contact-info-content h4 {
  margin-bottom: 10px;
  font-size: 20px;
}
.contact-info-content span {
  display: block;
}
.contact-info-content span a:hover {
  color: var(--clr-theme-secondary);
}

.contact-map iframe {
  height: 655px;
  width: 100%;
  mix-blend-mode: luminosity;
}

.contact-from {
  background: var(--clr-theme-primary);
  padding: 60px 60px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 1200px) and (max-width: 1399px) {
  .contact-from {
    padding: 40px 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-from {
    padding: 50px 50px;
  }
}
@media (max-width: 450px) {
  .contact-from {
    padding: 25px 20px;
  }
}

.contact__from-input {
  margin-bottom: 30px;
}
.contact__from-input input {
  width: 100%;
  height: 59px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 23px;
  background-color: var(--clr-common-white);
  font-size: 15px;
  border: none;
  color: var(--clr-text-body);
  outline: none;
}
.contact__from-input textarea {
  width: 100%;
  background-color: var(--clr-common-white);
  border: none;
  padding: 0px 25px;
  outline: 0;
  min-height: 185px;
  color: #777a7e;
  resize: none;
}
.contact__select {
  position: relative;
}
.contact__select:after {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  content: "";
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 8px solid var(--clr-theme-primary);
  right: 15px;
}
.contact__select select {
  height: 60px;
  padding: 0 23px;
  line-height: 60px;
  width: 100%;
  background-color: var(--clr-common-white);
  border: 0 !important; /*Removes border*/
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  text-overflow: "";
  text-indent: 0.01px; /* Removes default arrow from firefox*/
  text-overflow: ""; /*Removes default arrow from firefox*/
  color: var(--clr-common-placeholder);
}
.contact__select select::-ms-expand {
  display: none;
}

.contact__info-icon span {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  width: 50px;
  background: var(--clr-bg-4);
  border-radius: 5px;
}

.contact__info-item {
  text-align: center;
}

.contact__info-text {
  padding: 60px 27px 35px 27px;
  box-shadow: 0px 4.8px 24.4px 0px rgba(19, 16, 34, 0.1);
  margin-top: -25px;
  background: var(--clr-common-white);
  border-radius: 5px;
}
.contact__info-text h4 {
  font-weight: 700;
  margin-bottom: 5px;
}
.contact__info-text span {
  color: var(--clr-text-2);
  font-weight: 500;
}
.contact__info-text span a:hover {
  color: var(--clr-theme-primary);
}

.contact__info-main {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  position: relative;
  z-index: 11;
  max-width: 860px;
  margin: 0 auto;
}
@media only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .contact__info-main {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .contact__info-main {
    grid-template-columns: repeat(1, 1fr);
  }
}

/*----------------------------------------*/
/*  FOOTER CSS START
/*----------------------------------------*/
.footer-grid {
  display: grid;
  grid-template-columns: 35% 1fr 1fr auto;
  gap: 30px;
  padding-top: 120px;
  padding-bottom: 80px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-grid {
    grid-template-columns: auto auto;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-grid {
    grid-template-columns: auto;
  }
}

.input-wrapper input {
  width: 100%;
  height: 65px;
  background: rgba(217, 217, 217, 0.2);
  color: var(--clr-common-white);
  border: 0;
  padding-left: 30px;
  padding-right: 70px;
  border-radius: 72px;
  outline: none;
}
.input-wrapper input::placeholder {
  color: #9f9f9f;
}

.footer-link-2 ul li {
  list-style: none;
}
.footer-link-2 ul li:not(:last-child) {
  margin-bottom: 15px;
}
.footer-link-2 ul li a {
  color: var(--clr-text-3);
  position: relative;
  line-height: 1;
  text-transform: capitalize;
}
.footer-link-2 ul li a:hover {
  padding-left: 15px;
  color: var(--clr-common-white);
}
.footer-link-2 ul li a:hover:after {
  opacity: 1;
  visibility: visible;
  color: var(--clr-common-white);
}
.footer-link-2 ul li a::after {
  position: absolute;
  content: "\f105";
  font-family: var(--bd-ff-fontawesome);
  height: 100%;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  opacity: 0;
  visibility: hidden;
}

.footer-widget-title h4 {
  margin-bottom: 30px;
  color: var(--clr-common-white);
  font-size: 20px;
  font-weight: 600;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-title h4 {
    font-size: 20px;
  }
}

.footer-round-btn {
  position: absolute;
  height: 55px;
  width: 55px;
  background: var(--clr-theme-primary);
  font-size: 20px;
  color: var(--clr-common-white);
  border-radius: 50%;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
}
.footer-round-btn:hover {
  background-color: var(--clr-theme-secondary);
}

.footer-copyright-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px 30px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-copyright-wrapper {
    justify-content: center;
  }
}

.footer-copyright-area {
  padding: 35px 0;
}

.footer-conditions ul {
  display: flex;
  align-items: center;
  gap: 20px 20px;
}
.footer-conditions ul li {
  position: relative;
  list-style: none;
}
.footer-conditions ul li:not(:last-child)::before {
  position: absolute;
  content: "";
  height: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  background-color: var(--clr-common-white);
  right: -11px;
}
.footer-conditions ul li a {
  color: var(--clr-common-white);
}
.footer-conditions ul li a:hover {
  color: var(--clr-theme-secondary);
}

.footer-copyright-text p {
  color: var(--clr-common-white);
}
.footer-copyright-text p a:hover {
  color: var(--clr-theme-secondary);
}

.footer-widget-2 > p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 35px;
}

.footer-col-1 {
  padding-right: 15px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-col-1 {
    padding-right: 0;
  }
}

.footer-col-2 {
  padding-left: 50px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .footer-col-2 {
    padding-left: 0;
  }
}

.footer-col-3 {
  padding-left: 80px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .footer-col-3 {
    padding-left: 0;
  }
}

.footer-col-4 {
  padding-left: 50px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 576px) and (max-width: 767px),
  (max-width: 575px) {
  .footer-col-4 {
    padding-left: 0;
  }
}

.footer-link ul li {
  list-style: none;
}
.footer-link ul li:not(:last-child) {
  margin-bottom: 15px;
}
.footer-link ul li a {
  color: var(--clr-text-3);
  position: relative;
  line-height: 1;
  font-weight: 500;
  padding-left: 13px;
}
.footer-link ul li a:hover {
  color: var(--clr-theme-secondary);
}
.footer-link ul li a::after {
  position: absolute;
  content: "";
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: var(--clr-theme-secondary);
  width: 5px;
  height: 5px;
  border-radius: 50%;
}

.footer-widget > p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 27px;
  color: #c2c7c0;
}

.footer-opening-icon span {
  font-size: 17px;
  letter-spacing: -0.02em;
  color: var(--clr-common-white);
}

.footer-opening {
  max-width: 250px;
}
@media (max-width: 575px) {
  .footer-opening {
    max-width: 100%;
  }
}

.footer-opening-content h4 {
  font-size: 17px;
  color: var(--clr-common-white);
  margin-bottom: 10px;
}
.footer-opening-content p {
  font-weight: 500;
  font-size: 14px;
  color: var(--clr-text-3);
}

.footer-info-item {
  margin-bottom: 10px;
}

.footer-info-text a {
  font-weight: 500;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.7);
}
.footer-info-text a:hover {
  color: var(--clr-theme-secondary);
}

.footer-info-icon span {
  color: var(--clr-theme-secondary);
}

.footer-grid-3 {
  display: flex;
  gap: 50px 30px;
  justify-content: space-between;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-grid-3 {
    flex-wrap: wrap;
  }
}

.footer-widget-3:nth-child(1) {
  width: 25%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-3:nth-child(1) {
    width: 30%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-3:nth-child(1) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-3:nth-child(1) {
    width: 100%;
  }
}
@media (max-width: 450px) {
  .footer-widget-3:nth-child(1) {
    width: 100%;
  }
}
.footer-widget-3:nth-child(2) {
  width: 14%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-3:nth-child(2) {
    width: 18%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-3:nth-child(2) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-3:nth-child(2) {
    width: 45%;
  }
}
@media (max-width: 450px) {
  .footer-widget-3:nth-child(2) {
    width: 100%;
  }
}
.footer-widget-3:nth-child(3) {
  width: 14%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-3:nth-child(3) {
    width: 18%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-3:nth-child(3) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-3:nth-child(3) {
    width: 45%;
  }
}
@media (max-width: 450px) {
  .footer-widget-3:nth-child(3) {
    width: 100%;
  }
}
.footer-widget-3:nth-child(4) {
  width: 27%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-3:nth-child(4) {
    width: 25%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-3:nth-child(4) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-3:nth-child(4) {
    width: 100%;
  }
}
@media (max-width: 450px) {
  .footer-widget-3:nth-child(4) {
    width: 100%;
  }
}

.footer-style-3 .footer-widget-title h4 {
  color: var(--clr-common-heading);
}
.footer-style-3 .footer-widget > p {
  color: var(--clr-text-body);
}

.footer-working-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--clr-border-2);
}
.footer-working-info:not(:last-child) {
  margin-bottom: 20px;
}
.footer-working-info h6 {
  color: var(--clr-text-3);
}
.footer-working-info span {
  color: var(--clr-text-3);
  font-weight: 500;
}

.footer-style-3 .theme-social a {
  background: transparent;
  border: 1px solid var(--clr-border-2);
}
.footer-style-3 .theme-social a:hover {
  color: var(--clr-common-white);
  background-color: var(--clr-theme-secondary);
  border-color: transparent;
}

.footer-widget-4 > p {
  margin-bottom: 30px;
  color: var(--clr-text-3);
}

.footer-widget-4:nth-child(1) {
  width: 30%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-4:nth-child(1) {
    width: 30%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-4:nth-child(1) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-4:nth-child(1) {
    width: 100%;
  }
}
@media (max-width: 450px) {
  .footer-widget-4:nth-child(1) {
    width: 100%;
  }
}
.footer-widget-4:nth-child(2) {
  width: 20%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-4:nth-child(2) {
    width: 20%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-4:nth-child(2) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-4:nth-child(2) {
    width: 45%;
  }
}
@media (max-width: 450px) {
  .footer-widget-4:nth-child(2) {
    width: 100%;
  }
}
.footer-widget-4:nth-child(3) {
  width: 20%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-4:nth-child(3) {
    width: 20%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-4:nth-child(3) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-4:nth-child(3) {
    width: 45%;
  }
}
@media (max-width: 450px) {
  .footer-widget-4:nth-child(3) {
    width: 100%;
  }
}
.footer-widget-4:nth-child(4) {
  width: 10%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-4:nth-child(4) {
    width: 15%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-4:nth-child(4) {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-4:nth-child(4) {
    width: 100%;
  }
}
@media (max-width: 450px) {
  .footer-widget-4:nth-child(4) {
    width: 100%;
  }
}

.footer-payment-item:not(:last-child) {
  margin-bottom: 10px;
}

.footer-style-4 .footer-link ul li a {
  padding-left: 0;
}
.footer-style-4 .footer-link ul li a:hover {
  padding-left: 15px;
  color: var(--clr-common-white);
}
.footer-style-4 .footer-link ul li a:hover::after {
  opacity: 1;
  color: var(--clr-common-white);
}
.footer-style-4 .footer-link ul li a::after {
  background-color: transparent;
  content: "\f105";
  font-family: var(--bd-ff-fontawesome);
  left: 0;
  top: 5px;
  opacity: 0;
}

.footer-widget-4-1 > p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 35px;
}

.footer-style-5 .footer-widget-4-1 > p {
  color: var(--clr-text-body);
  margin-bottom: 35px;
}
.footer-style-5 .footer-widget-title h4 {
  color: var(--clr-common-heading);
}
.footer-style-5 .footer-copyright-text p {
  color: var(--clr-common-heading);
}
.footer-style-5 .footer-conditions ul li a {
  color: var(--clr-common-heading);
}
.footer-style-5 .footer-conditions ul li a:hover {
  color: var(--clr-theme-secondary);
}
.footer-style-5 .footer-conditions ul li:not(:last-child)::before {
  background-color: var(--clr-border-1);
}
.footer-style-5 .footer-copyright-area {
  border-top: 1px solid rgba(22, 22, 22, 0.15);
}

.footer-info-text-2 p a {
  color: rgba(85, 85, 85, 0.69);
}
.footer-info-text-2 span {
  font-size: 18px;
  color: var(--clr-theme-primary);
}
.footer-info-text-2 span a:hover {
  color: var(--clr-theme-secondary);
}

.footer-grid-5 {
  display: flex;
  gap: 50px 30px;
  justify-content: space-between;
  align-items: start;
  padding-bottom: 80px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-grid-5 {
    flex-wrap: wrap;
  }
}

.footer-widget-5-1 {
  width: 40%;
  padding-right: 100px;
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px),
  only screen and (min-width: 768px) and (max-width: 991px),
  only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-5-1 {
    padding-right: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-5-1 {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-5-1 {
    width: 100%;
  }
}
@media (max-width: 450px) {
  .footer-widget-5-1 {
    width: 100%;
  }
}

.footer-widget-5-2 {
  width: 25%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-5-2 {
    width: 30%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-5-2 {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-5-2 {
    width: 45%;
  }
}
@media (max-width: 450px) {
  .footer-widget-5-2 {
    width: 100%;
  }
}

.footer-widget-5-3 {
  width: 25%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-5-3 {
    width: 30%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-5-3 {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-5-3 {
    width: 45%;
  }
}
@media (max-width: 450px) {
  .footer-widget-5-3 {
    width: 100%;
  }
}

.footer-widget-5-4 {
  width: 25%;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-5-4 {
    width: 30%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-5-4 {
    width: 45%;
  }
}
@media (max-width: 575px),
  only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-5-4 {
    width: 100%;
  }
}
@media (max-width: 450px) {
  .footer-widget-5-4 {
    width: 100%;
  }
}

/*# sourceMappingURL=main.css.map */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 1rem;
    font-family: "Montserrat", sans-serif;
    text-align: left;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 12px 15px;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f0f0; /* Light pink shade */
    color: #000;
    font-weight: 600;
    text-transform: uppercase;
}

tr:nth-child(even) {
    background-color: #fde7e7; /* Soft pink background */
}

tr:hover {
    background-color: #fcd9d9; /* Slightly darker pink on hover */
    transition: 0.3s ease-in-out;
}

@media (max-width: 600px) {
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    th, td {
        padding: 10px;
    }
}
